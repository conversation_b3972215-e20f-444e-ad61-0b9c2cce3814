////////////////////////////////////////////////////////////////////////////////
//
// Code::Blocks new file wizard script
//
// Project: fortran source file
// Author:  <PERSON>
//
////////////////////////////////////////////////////////////////////////////////

function BeginWizard()
{
    // this is the text that will appear in the start (intro) page
    local intro_msg = _T("Welcome to the new Java source file wizard!\n" +
                         "This wizard will guide you to create a new Java source file.\n\n" +
                         "When you 're ready to proceed, please click \"Next\"...");

    // add builtin pages
    Wizard.AddInfoPage(_T("CFileIntro"), intro_msg); // intro
    Wizard.AddFilePathPage(false); // select filename (no header guard for source files)

	Wizard.SetFilePathSelectionFilter(_T("Java source files (*.java)|*.java"));
}

function CreateFiles()
{
    local fname = Wizard.GetFileName();
    local ed    = GetEditorManager();
    if (IsNull(ed))
    {
        ShowError(_T("The wizard could not locate the editor manager."));
    }

    local ed_new = ed.New(fname);
    if (IsNull(ed_new))
    {
        ShowError(_T("The wizard could not create a new file.\n" +
                     "Maybe the target folder is write-protected?"));
    }
    else
    {
        // succeeded -> add file to project if needed
        if (Wizard.GetFileAddToProject())
        {
            AddFileToTargets(Wizard, fname);
        }
    }
    return fname;
}
