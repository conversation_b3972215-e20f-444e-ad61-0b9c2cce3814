<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="3" />
	<Project>
		<Option title="wxWidgets application" />
		<Option pch_mode="0" />
		<Option compiler="gcc" />
		<Build>
			<Target title="default">
				<Option output="wxwidgets.exe" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Option includeInTargetAll="1" />
				<Option projectResourceIncludeDirsRelation="0" />
			</Target>
			<Environment>
				<Variable name="WX_CFG" value="" />
			</Environment>
		</Build>
		<Compiler>
			<Add option="-pipe" />
			<Add option="-mthreads" />
			<Add option="-Winvalid-pch" />
			<Add option="-D__GNUWIN32__" />
			<Add option="-D__WXMSW__" />
			<Add option="-DWXUSINGDLL" />
			<Add option="-DUSE_PCH" />
			<Add directory="$(#WX.include)" />
			<Add directory="$(#WX.lib)\gcc_dll$(WX_CFG)\msw" />
		</Compiler>
		<ResourceCompiler>
			<Add directory="$(#WX.include)" />
		</ResourceCompiler>
		<Linker>
			<Add library="wxmsw26" />
			<Add directory="$(#WX.lib)" />
			<Add directory="$(#WX.lib)\gcc_dll$(WX_CFG)" />
		</Linker>
		<Unit filename="wx_pch.h">
			<Option compilerVar="CPP" />
			<Option link="0" />
			<Option weight="0" />
			<Option target="default" />
		</Unit>
	</Project>
</CodeBlocks_project_file>
