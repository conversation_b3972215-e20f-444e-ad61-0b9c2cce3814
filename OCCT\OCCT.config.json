{"Version": 1200, "WindowTop": 21, "WindowLeft": 249, "WindowWidth": 1525, "WindowHeight": 900, "WindowState": 2, "EnabledTemperatureSensors": [{"DeviceName": "CPU [#0]: Intel Core i9-10900KF: DTS", "Name": "Core 0", "LocationIndex": 0}, {"DeviceName": "CPU [#0]: Intel Core i9-10900KF: DTS", "Name": "CPU Package", "LocationIndex": 0}, {"DeviceName": "Intel Core i9-10900KF", "Name": "CPU Package", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Temperature", "LocationIndex": 0}], "EnabledVoltageSensors": [{"DeviceName": "Intel Core i9-10900KF", "Name": "Core 0 VID", "LocationIndex": 0}, {"DeviceName": "GIGABYTE Z490M GAMING X (ITE IT8688E)", "Name": "+12V", "LocationIndex": 0}, {"DeviceName": "GIGABYTE Z490M GAMING X (ITE IT8688E)", "Name": "+5V", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Core Voltage", "LocationIndex": 0}], "EnabledFanSensors": [{"DeviceName": "GIGABYTE Z490M GAMING X (ITE IT8688E)", "Name": "System 1", "LocationIndex": 1}, {"DeviceName": "GIGABYTE Z490M GAMING X (ITE IT8688E)", "Name": "System 2 (VRM)", "LocationIndex": 2}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Fan1", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Fan2", "LocationIndex": 0}], "EnabledPowerSensors": [{"DeviceName": "Intel Core i9-10900KF", "Name": "CPU Package Power", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Power", "LocationIndex": 0}], "EnabledCurrentSensors": [], "EnabledFrequencySensors": [{"DeviceName": "Intel Core i9-10900KF", "Name": "Core 0 Clock", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Clock", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Memory Clock", "LocationIndex": 0}], "EnabledUsageSensors": [{"DeviceName": "System", "Name": "Physical Memory Load", "LocationIndex": 0}, {"DeviceName": "Intel Core i9-10900KF", "Name": "Total CPU Usage", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Core Load", "LocationIndex": 0}, {"DeviceName": "NVIDIA GeForce RTX 3070", "Name": "GPU Memory Usage", "LocationIndex": 0}], "EnabledOtherSensors": [], "SensorClassStates": [{"SensorClass": "Temperature", "IsEnabled": true}, {"SensorClass": "Voltage", "IsEnabled": true}, {"SensorClass": "Power", "IsEnabled": true}, {"SensorClass": "Current", "IsEnabled": true}, {"SensorClass": "Fan", "IsEnabled": true}, {"SensorClass": "Frequency", "IsEnabled": true}, {"SensorClass": "Usage", "IsEnabled": true}, {"SensorClass": "Other", "IsEnabled": true}], "DeviceTypeStates": [{"DeviceType": "Cpu", "IsEnabled": true}, {"DeviceType": "Gpu", "IsEnabled": false}, {"DeviceType": "Motherboard", "IsEnabled": false}, {"DeviceType": "Storage", "IsEnabled": false}, {"DeviceType": "Memory", "IsEnabled": false}, {"DeviceType": "Other", "IsEnabled": false}], "MonitoringUpdateInterval": 2, "MonitoringUpdateIntervalUnit": "Seconds", "GraphUpdateInterval": 2, "GraphUpdateIntervalUnit": "Seconds", "TemperatureAlertValue": 90, "IsTemperatureAlertValueEnabled": false, "Language": "English", "Monitoring": true, "MonitoringTableWidth": 700, "SelectedMonitoringIndex": 0, "SelectedArea": 0, "CheckForUpdates": "Stable", "SaveReportPath": "C:\\Users\\<USER>\\Documents", "SaveScreenshotPath": "C:\\Users\\<USER>\\Documents", "MotherboardName": "GIGABYTE Z490M GAMING X", "SelectedComponentUsage": "System", "MonitoringShowOnlyEnabledSensors": false, "MonitoringAreaDisplayMode": "TableAndGraph", "StopOnError": false, "StopOnWheaError": false, "MonitoringEcEnable": true, "MonitoringSmBusEnable": true, "MonitoringGpuI2CEnable": false, "MonitoringDriveScanEnable": false, "MonitoringCsmiSasEnable": false, "MonitoringImeEnable": false, "MonitoringCorsairAsetekEnable": false, "MonitoringDebugModeEnable": false, "AllowOcbaseUpload": true, "Skin": 1, "FontSizeOffset": 0.0, "LicenseExpirationWarningLastDisplay": 999, "StabilitySchedule": {"ScheduleType": "Stability", "StabilityCertificateType": "System", "Periods": [{"Duration": "00:30:00", "Version": "800", "IsInfinite": false, "TestType": "CpuOcct", "CpuOcctConfig": {"DataSet": "Large", "OcctInstructionSet": "Auto", "LoadType": "Variable", "Mode": "Extreme", "Threads": "Auto", "FixedThreadCount": 0, "StartAtCycle": 1, "AdvancedThreadConfig": {"ThreadSettingsThreads": "Physical", "CycleActiveCore": "Disabled", "SwapActiveCore": "Disabled", "CycleActiveCoreInterval": 3, "CycleActiveCoreIntervalUnit": "Seconds", "SwapActiveCoreInterval": 5, "SwapActiveCoreIntervalUnit": "Seconds", "CoreActive": [0]}}, "CpuLinpackConfig": {"Version": "v2019", "Threads": "PhysicalAndVirtual", "Memory": 2048}, "MemoryConfig": {"MemoryInPercent": 80, "MemoryMemUnit": "Percent", "Threads": "Auto", "FixedThreadCount": 0, "InstructionSet": "Auto"}, "Gpu3dConfig": {"Mode": "ErrorDetection", "UsageLimit": 100, "ShaderComplexity": 3, "GpuIndexes": [0], "TestAllGpus": false}, "GpuUnrealConfig": {"IntensityType": "Variable", "SteadyIntensityType": "Light", "SteadyLoad": 30, "VariableIntensityIncreaseIntervalTimeSpan": "00:00:30", "VariableIntensityIncreaseStep": 4, "VariableMinIntensity": 10, "VariableMaxIntensity": 60, "SwitchInterval": 500, "SwitchFirstIntensity": 20, "SwitchSecondIntensity": 80, "GpuIndexes": [0], "TestAllGpus": false}, "VramConfig": {"GpuIndex": 0, "MemoryInMiB": 0, "MemoryInPercent": 80, "MemoryMemUnit": "Percent"}, "MonitoringOnlyConfig": {"Type": 0}, "PowerSupplyConfig": {"CpuOcctInstructionSet": "Auto", "GpuIndexes": [0]}, "CpuBenchmarkConfig": {"Run": 0, "Threads": "Single", "InstructionSet": "Sse"}, "MemoryBenchmarkConfig": {"Run": 0, "BenchmarkType": "Read"}}]}, "BenchmarkSchedule": {"ScheduleType": "Benchmark", "StabilityCertificateType": "System", "Periods": [{"Duration": "00:30:00", "Version": "800", "IsInfinite": true, "TestType": "CpuBenchmark", "CpuOcctConfig": {"DataSet": "Large", "OcctInstructionSet": "Auto", "LoadType": "Variable", "Mode": "Normal", "Threads": "Auto", "FixedThreadCount": 0, "StartAtCycle": 1, "AdvancedThreadConfig": {"ThreadSettingsThreads": "Physical", "CycleActiveCore": "Disabled", "SwapActiveCore": "Disabled", "CycleActiveCoreInterval": 3, "CycleActiveCoreIntervalUnit": "Seconds", "SwapActiveCoreInterval": 5, "SwapActiveCoreIntervalUnit": "Seconds", "CoreActive": [0]}}, "CpuLinpackConfig": {"Version": "v2019", "Threads": "PhysicalAndVirtual", "Memory": 2048}, "MemoryConfig": {"MemoryInPercent": 80, "MemoryMemUnit": "Percent", "Threads": "Auto", "FixedThreadCount": 0, "InstructionSet": "Auto"}, "Gpu3dConfig": {"Mode": "NoErrorDetection", "UsageLimit": 100, "ShaderComplexity": 3, "GpuIndexes": [0], "TestAllGpus": false}, "GpuUnrealConfig": {"IntensityType": "Variable", "SteadyIntensityType": "Light", "SteadyLoad": 30, "VariableIntensityIncreaseIntervalTimeSpan": "00:00:30", "VariableIntensityIncreaseStep": 4, "VariableMinIntensity": 10, "VariableMaxIntensity": 60, "SwitchInterval": 500, "SwitchFirstIntensity": 20, "SwitchSecondIntensity": 80, "GpuIndexes": [0], "TestAllGpus": false}, "VramConfig": {"GpuIndex": 0, "MemoryInMiB": 0, "MemoryInPercent": 80, "MemoryMemUnit": "Percent"}, "MonitoringOnlyConfig": {"Type": 0}, "PowerSupplyConfig": {"CpuOcctInstructionSet": "Auto", "GpuIndexes": [0]}, "CpuBenchmarkConfig": {"Run": 0, "Threads": "Multiple", "InstructionSet": "Avx"}, "MemoryBenchmarkConfig": {"Run": 0, "BenchmarkType": "Read"}}]}, "StabilityCertificateSchedule": {"ScheduleType": "StabilityCertificate", "StabilityCertificateType": "System", "StabilityCertificateConfigs": [{"StabilityCertificateType": "Cpu", "StabilityCertificateLevel": "Bronze", "GpuIndexes": [], "GpuNames": []}, {"StabilityCertificateType": "Gpu", "StabilityCertificateLevel": "Bronze", "GpuIndexes": [], "GpuNames": []}, {"StabilityCertificateType": "Memory", "StabilityCertificateLevel": "Bronze", "GpuIndexes": [], "GpuNames": []}, {"StabilityCertificateType": "System", "StabilityCertificateLevel": "Bronze", "GpuIndexes": [0], "GpuNames": ["GeForce RTX 3070"]}, {"StabilityCertificateType": "SystemNo3d", "StabilityCertificateLevel": "Bronze", "GpuIndexes": [], "GpuNames": []}], "Periods": []}, "OptimizeEditionOptimizeSchedule": {"ScheduleType": "CpuOptimize", "StabilityCertificateType": "System", "Periods": []}, "SelectedMonitoringSummaryCpuIndex": 0, "SelectedMonitoringSummaryGpuIndex": 0}