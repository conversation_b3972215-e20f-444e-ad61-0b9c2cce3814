﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <RegEx name="Fatal error"
           type="error"
           msg="1">
        <![CDATA[FATAL:[ \t]*(.*)]]>
    </RegEx>
    <RegEx name="'In function...' info"
           type="info"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ \t]+([iI]n ([cC]lass|[cC]onstructor|[dD]estructor|[fF]unction|[mM]ember [fF]unction).*)]]>
    </RegEx>
    <RegEx name="'Skipping N instantiation contexts' info (2)"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t]+(\[[ \t]+[Ss]kipping [0-9]+ instantiation contexts[ \t]+\])]]>
    </RegEx>
    <RegEx name="'Skipping N instantiation contexts' info"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]+(\[[ \t]+[Ss]kipping [0-9]+ instantiation contexts[ \t]+\])]]>
    </RegEx>
    <RegEx name="'In instantiation' warning"
           type="warning"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ \t]+([Ii]n [Ii]nstantiation.*)]]>
    </RegEx>
    <RegEx name="'Required from' warning"
           type="warning"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t]+([Rr]equired from.*)]]>
    </RegEx>
    <RegEx name="'Instantiated from' info (2)"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t]+([Ii]nstantiated from .*)]]>
    </RegEx>
    <RegEx name="'Instantiated from' info"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]+([Ii]nstantiated from .*)]]>
    </RegEx>
    <RegEx name="Resource compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[windres.exe:[ \t]([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error (2)"
           type="error"
           msg="1">
        <![CDATA[windres.exe:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Preprocessor warning"
           type="warning"
           msg="4"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):([0-9]+):[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Compiler note (2)"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t]([Nn]ote:[ \t].*)]]>
    </RegEx>
    <RegEx name="Compiler note"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]([Nn]ote:[ \t].*)]]>
    </RegEx>
    <RegEx name="General note"
           type="info"
           msg="1">
        <![CDATA[([Nn]ote:[ \t].*)]]>
    </RegEx>
    <RegEx name="Preprocessor error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Compiler warning (2)"
           type="warning"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Compiler warning"
           type="warning"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Undefined reference (2)"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[[][{}() \t#%$~[:alnum:]&_:+/\.-]+\.o:([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](undefined reference.*)]]>
    </RegEx>
    <RegEx name="Compiler error (2)"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker warning"
           type="warning"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):\(\.text\+[0-9a-fA-FxX]+\):[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Linker error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (2)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[[][{}() \t#%$~[:alnum:]&_:+/\.-]+\(.text\+[0-9A-Za-z]+\):([ \tA-Za-z0-9_:+/\.-]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (3)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):\(\.text\+[0-9a-fA-FxX]+\):(.*)]]>
    </RegEx>
    <RegEx name="Linker error (lib not found)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[.*(ld.*):[ \t](cannot find.*)]]>
    </RegEx>
    <RegEx name="Linker error (cannot open output file)"
           type="error"
           msg="2;3"
           file="1">
        <![CDATA[.*(ld.*):[ \t](cannot open output file.*):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (unrecognized option)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[.*(ld.*):[ \t](unrecognized option.*)]]>
    </RegEx>
    <RegEx name="Compiler error (unrecognized option)"
           type="error"
           msg="1">
        <![CDATA[.*cc.*:[ \t]([Uu]nrecognized.*option.*)]]>
    </RegEx>
    <RegEx name="No such file or directory"
           type="error"
           msg="2"
           file="1">
        <![CDATA[.*:(.*):[ \t](No such file or directory.*)]]>
    </RegEx>
    <RegEx name="Undefined reference"
           type="error"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ \t](undefined reference.*)]]>
    </RegEx>
    <RegEx name="General error"
           type="error"
           msg="1">
        <![CDATA[([Ee]rror:[ \t].*)]]>
    </RegEx>
    <RegEx name="General warning"
           type="warning"
           msg="1">
        <![CDATA[([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Auto-import info"
           type="info"
           msg="1">
        <![CDATA[([Ii]nfo:[ \t].*)\(auto-import\)]]>
    </RegEx>
    <RegEx name="Linker warning (different sized sections)"
           type="warning"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ \t]+(duplicate section.*has different size)]]>
    </RegEx>
</CodeBlocks_compiler_options>
