<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="5" />
    <object class="Project" expanded="1">
        <property name="bitmaps"></property>
        <property name="code_generation">C++</property>
        <property name="encoding">UTF-8</property>
        <property name="file">GUIFrame</property>
        <property name="first_id">1000</property>
        <property name="icons"></property>
        <property name="internationalize">0</property>
        <property name="name">MyProject</property>
        <property name="path">.</property>
        <property name="precompiled_header">wx/wxprec.h</property>
        <property name="relative_path">1</property>
        <property name="use_enum">0</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Frame" expanded="1">
            <property name="bg"></property>
            <property name="center"></property>
            <property name="enabled">1</property>
            <property name="extra_style"></property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">GUIFrame</property>
            <property name="pos"></property>
            <property name="size">481,466</property>
            <property name="style">wxDEFAULT_FRAME_STYLE</property>
            <property name="subclass"></property>
            <property name="title">wxWidgets Application Template</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <property name="xrc_skip_sizer">1</property>
            <event name="OnClose">OnClose</event>
            <event name="OnSize"></event>
            <object class="wxMenuBar" expanded="1">
                <property name="bg"></property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="font"></property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label"></property>
                <property name="maximum_size"></property>
                <property name="minimum_size"></property>
                <property name="name">mbar</property>
                <property name="permission">protected</property>
                <property name="pos"></property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_style"></property>
                <object class="wxMenu" expanded="1">
                    <property name="label">&amp;File</property>
                    <property name="name">fileMenu</property>
                    <object class="wxMenuItem" expanded="1">
                        <property name="bitmap"></property>
                        <property name="checked">0</property>
                        <property name="enabled">1</property>
                        <property name="help">Quit the application</property>
                        <property name="id">idMenuQuit</property>
                        <property name="kind">wxITEM_NORMAL</property>
                        <property name="label">&amp;Quit</property>
                        <property name="name">menuFileQuit</property>
                        <property name="shortcut">Alt+F4</property>
                        <property name="unchecked_bitmap"></property>
                        <event name="OnMenuSelection">OnQuit</event>
                    </object>
                </object>
                <object class="wxMenu" expanded="1">
                    <property name="label">&amp;Help</property>
                    <property name="name">helpMenu</property>
                    <object class="wxMenuItem" expanded="1">
                        <property name="bitmap"></property>
                        <property name="checked">0</property>
                        <property name="enabled">1</property>
                        <property name="help">Show info about this application</property>
                        <property name="id">idMenuAbout</property>
                        <property name="kind">wxITEM_NORMAL</property>
                        <property name="label">&amp;About</property>
                        <property name="name">menuHelpAbout</property>
                        <property name="shortcut">F1</property>
                        <property name="unchecked_bitmap"></property>
                        <event name="OnMenuSelection">OnAbout</event>
                    </object>
                </object>
            </object>
            <object class="wxStatusBar" expanded="1">
                <property name="bg"></property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="fields">2</property>
                <property name="font"></property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="maximum_size"></property>
                <property name="minimum_size"></property>
                <property name="name">statusBar</property>
                <property name="permission">protected</property>
                <property name="pos"></property>
                <property name="size"></property>
                <property name="style">wxST_SIZEGRIP</property>
                <property name="subclass"></property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_style"></property>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
