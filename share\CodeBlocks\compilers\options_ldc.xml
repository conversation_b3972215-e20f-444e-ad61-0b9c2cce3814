﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <if platform="windows">
        <Program name="C"         value="ldc.exe"/>
        <Program name="CPP"       value="ldc.exe"/>
        <Program name="LD"        value="ldc.exe"/>
        <Program name="DBGconfig" value="gdb_debugger:Default"/>
        <Program name="LIB"       value="ar.exe"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make.exe"/>
    </if>
    <else>
        <Program name="C"         value="ldc"/>
        <Program name="CPP"       value="ldc"/>
        <Program name="LD"        value="ldc"/>
        <Program name="DBGconfig" value="gdb_debugger:Default"/>
        <Program name="LIB"       value="ar"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make"/>
    </else>

    <Switch name="includeDirs"             value="-I"/>
    <Switch name="libDirs"                 value="-L-L"/>
    <Switch name="linkLibs"                value="-L-l"/>
    <Switch name="defines"                 value="-d-version="/>
    <Switch name="genericSwitch"           value="-"/>
    <Switch name="objectExtension"         value="o"/>
    <Switch name="needDependencies"        value="true"/>
    <Switch name="forceCompilerUseQuotes"  value="false"/>
    <Switch name="forceLinkerUseQuotes"    value="false"/>
    <Switch name="logging"                 value="default"/>
    <Switch name="libPrefix"               value="lib"/>
    <Switch name="libExtension"            value="a"/>
    <Switch name="linkerNeedsLibPrefix"    value="false"/>
    <Switch name="linkerNeedsLibExtension" value="false"/>

    <Option name="Produce debugging symbols"
            option="-g"
            category="Debugging"
            checkAgainst="-O1 -O2 -O3"
            checkMessage="You have optimizations enabled. This is Not A Good Thing(tm) when producing debugging symbols..."/>

    <Option name="Enable warnings"
            option="-w"
            category="Warnings"/>

    <Category name="D features">
        <Option name="allow deprecated features"
                option="-d"/>
        <Option name="inline expand functions"
                option="-enable-inlining"/>
        <Option name="enable array bounds checks"
                option="-enable-boundscheck"/>
        <Option name="enable contracts"
                option="-enable-contracts"/>
        <Option name="enable assertions"
                option="-enable-asserts"/>
        <Option name="enable invariants"
                option="-enable-invariants"/>
    </Category>

    <Category name="Optimization"
              exclusive="true">
        <Option name="No optimization"
                option="-O0"/>
        <Option name="Simple optimizations"
                option="-O1"/>
        <Option name="Good optimizations"
                option="-O2"/>
        <Option name="Aggressive optimizations"
                option="-O3"/>
    </Category>

    <Category name="CPU architecture tuning"
              exclusive="true">
        <Option name="32-bit X86: Pentium-Pro and above"
                option="-march=x86"/>
        <Option name="64-bit X86: EM64T and AMD64"
                option="-march=x86-64"/>
    </Category>

    <Command name="CompileObject"
             value="$compiler $options $includes -c $file -of=$object"/>
    <Command name="GenDependencies"
             value="$compiler $options -deps=$dep_object $includes $file"/>
    <Command name="CompileResource"
             value="$rescomp $res_includes $res_options -J rc -O coff -i $file -o $resource_output"/>
    <Command name="LinkConsoleExe"
             value="$linker $libdirs -of=$exe_output $link_objects $link_resobjects $link_options $libs"/>
    <Command name="LinkExe"
             value="$linker $libdirs -of=$exe_output $link_objects $link_resobjects $link_options $libs"/>
    <Command name="LinkDynamic"
             value="$linker -shared $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    <Command name="LinkStatic"
             value="$lib_linker -r $static_output $link_objects\nranlib $static_output"/>
    <Command name="LinkNative"
             value="$linker $libdirs -of=$exe_output $link_objects $link_resobjects $link_options $libs"/>

    <RegEx name="Fatal error"
           type="error"
           msg="1">
        <![CDATA[FATAL:[ \t]*(.*)]]>
    </RegEx>
    <RegEx name="'Instantiated from here' info"
           type="normal"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]+([iI]nstantiated from here.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[windres.exe:[ \t]([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[windres.exe:[ \t]([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error (2)"
           type="error"
           msg="1">
        <![CDATA[windres.exe:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Compiler warning"
           type="warning"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+)\(([0-9]+)\):[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+)\(([0-9]+)\):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (2)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[[][{}() \t#%$~[:alnum:]&_:+/\.-]+\(.text\+[0-9A-Za-z]+\):([ \tA-Za-z0-9_:+/\.-]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (lib not found)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[.*(ld.*):[ \t](cannot find.*)]]>
    </RegEx>
    <RegEx name="Undefined reference"
           type="error"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ \t](undefined reference.*)]]>
    </RegEx>
    <RegEx name="General warning"
           type="warning"
           msg="1">
        <![CDATA[([Ww]arning:[ \t].*)]]>
    </RegEx>
</CodeBlocks_compiler_options>
