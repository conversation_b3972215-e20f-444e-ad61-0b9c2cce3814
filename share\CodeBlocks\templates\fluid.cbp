<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="4" />
	<Project>
		<Option title="FLTK Application" />
		<Option pch_mode="0" />
		<Option compiler="gcc" />
		<Build>
			<Target title="default">
				<Option output="fltk.exe" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Option includeInTargetAll="1" />
			</Target>
			<Environment>
				<Variable name="FLTK_DIR" value="C:\fltk-1.1.6" />
			</Environment>
		</Build>
		<Compiler>
			<Add option="-pedantic" />
			<Add option="-Wall" />
			<Add option="-mms-bitfields" />
			<Add option="-DWIN32" />
			<Add directory="$(FLTK_DIR)\include" />
		</Compiler>
		<Linker>
			<Add library="fltk" />
			<Add library="gdi32" />
			<Add library="user32" />
			<Add library="kernel32" />
			<Add library="ole32" />
			<Add library="uuid" />
			<Add library="comctl32" />
			<Add library="wsock32" />
			<Add library="m" />
			<Add directory="$(FLTK_DIR)\lib" />
		</Linker>
		<Unit filename="main.cpp">
			<Option compilerVar="CPP" />
			<Option target="default" />
		</Unit>
		<Unit filename="main_ui.cxx">
			<Option compilerVar="CPP" />
			<Option target="default" />
		</Unit>
		<Unit filename="main_ui.h">
			<Option compilerVar="CPP" />
			<Option compile="0" />
			<Option link="0" />
			<Option target="default" />
		</Unit>
	</Project>
</CodeBlocks_project_file>