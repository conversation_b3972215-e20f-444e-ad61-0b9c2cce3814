<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
        <Lexer name="JavaScript"
               index="3"
               filemasks="*.js">
                <Style name="Default"
                       index="0,11"
                       fg="0,0,0"
                       bg="255,255,255"
                       bold="0"
                       italics="0"
                       underlined="0"/>
                <Style name="Comment (normal)"
                       index="1,23,65,87"
                       fg="160,160,160"/>
                <Style name="Comment line (normal)"
                       index="2,66"
                       fg="190,190,230"/>
                <Style name="Comment (documentation)"
                       index="3,24,67,88"
                       fg="128,128,255"
                       bold="1"/>
                <Style name="Comment line (documentation)"
                       index="15,79"
                       fg="128,128,255"
                       bold="1"/>
                <Style name="Comment keyword (documentation)"
                       index="17,81"
                       fg="0,128,128"/>
                <Style name="Comment keyword error (documentation)"
                       index="18,82"
                       fg="128,0,0"/>
                <Style name="Number"
                       index="4"
                       fg="240,0,240"/>
                <Style name="Keyword"
                       index="5"
                       fg="0,0,160"
                       bold="1"/>
                <Style name="User keyword"
                       index="16"
                       fg="0,160,0"
                       bold="1"/>
                <Style name="Global classes and typedefs"
                       index="19"
                       fg="190,0,190"
                       bold="1"/>
                <Style name="String"
                       index="6,7,12"
                       fg="0,0,255"/>
                <Style name="UUID"
                       index="8"
                       fg="0,0,0"/>
                <Style name="Operator"
                       index="10"
                       fg="255,0,0"/>
                <Style name="Breakpoint line"
                       index="-2"
                       bg="255,160,160"/>
                <Style name="Debugger active line"
                       index="-3"
                       bg="160,160,255"/>
                <Style name="Compiler error line"
                       index="-4"
                       bg="255,128,0"/>
                <Keywords>
                        <!-- Primary keywords and identifiers -->
                        <Set index="0"
                             value="break case catch continue debugger default delete do else export finally for
                                    function if import in instanceof new return switch this throw try typeof var
                                    void while with"/>
                        <!-- Secondary keywords and identifiers -->
                        <Set index="1"
                             value="false Infinity NaN null true undefined

                                    constructor global ignoreCase lastIndex length message multiline name
                                    NEGATIVE_INFINITY POSITIVE_INFINITY prototype source"/>
                        <!-- Global classes and functions -->
                        <Set index="3"
                             value="Array Boolean Date Error EvalError Function Math Number Object RangeError RegExp
                                    String SyntaxError TypeError URIError

                                    abs acos apply asin atan atan2 call ceil charAt charCodeAt concat cos decodeURI
                                    decodeURIComponent encodeURI encodeURIComponent escape eval exec exp floor
                                    fromCharCode getDate getDay getFullYear getHours getMilliseconds getMinutes
                                    getMonth getSeconds getTime getTimezoneOffset getUTCDate getUTCDay
                                    getUTCFullYear getUTCHours getUTCMilliseconds getUTCMinutes getUTCMonth
                                    getUTCSeconds getYear hasOwnProperty indexOf isFinite isNaN isPrototypeOf join
                                    lastIndexOf localeCompare log match max min parse parseFloat parseInt pop pow
                                    propertyIsEnumerable push random replace reverse round search setDate
                                    setFullYear setHours setMilliseconds setMinutes setMonth setSeconds setTime
                                    setUTCDate setUTCFullYear setUTCHours setUTCMilliseconds setUTCMinutes
                                    setUTCMonth setUTCSeconds setYear shift sin slice sort split sqrt substr
                                    substring tan toDateString toExponential toFixed toGMTString toLocaleDateString
                                    toLocaleLowerCase toLocaleString toLocaleTimeString toLocaleUpperCase
                                    toLowerCase toPrecision toString toTimeString toUpperCase toUTCString unescape
                                    unshift UTC valueOf

                                    alert document history location navigator prompt screen window"/>
                </Keywords>
                <SampleCode value="lexer_javascript.sample"
                            breakpoint_line="20"
                            debug_line="22"
                            error_line="23"/>
                <LanguageAttributes
                    LineComment="//"
                    DoxygenLineComment="///"
                    StreamCommentStart="/*"
                    StreamCommentEnd="*/"
                    DoxygenStreamCommentStart="/**"
                    DoxygenStreamCommentEnd="*/"
                    BoxCommentStart="/* "
                    BoxCommentMid=" * "
                    BoxCommentEnd=" */"
                    CaseSensitive="1"
                    LexerCommentStyles="1,2,3,15,17,18,23,65,66,67,79,81,82,87"
                    LexerCharacterStyles=""
                    LexerStringStyles="6,7,12,70,76,71"
                    LexerPreprocessorStyles="9,73"/>
        </Lexer>
</CodeBlocks_lexer_properties>
