/*====================================================================
* Project: Board Support Package (BSP)
* Developed using:
* Function: Standard definitions for ATMEL ARM7TDMI controller AT91M55800
*
* Copyright HighTec EDV-Systeme GmbH 1982-2006
*====================================================================*/

#ifndef __AT91M55800_H__
#define __AT91M55800_H__


#define SYS_MCKI		32000000	/* maximal clock frequency [Hz] */


/* general register definition macro */
#define __REG32(x)	((volatile unsigned int *)(x))
#define __REG16(x)	((volatile unsigned short *)(x))
#define __REG8(x)	((volatile unsigned char *)(x))


/*-------------------------------------*/
/* Peripheral and Interrupt Identifier */
/*-------------------------------------*/

#define FIQ_ID		 0		/* Fast Interrupt */

#define SW_ID		 1		/* Soft Interrupt (generated by the AIC) */

#define US0_ID		 2		/* USART Channel 0 */
#define US1_ID		 3		/* USART Channel 1 */
#define US2_ID		 4		/* USART Channel 2 */

#define SPI_ID		 5		/* SPI Channel */

#define TC0_ID		 6		/* Timer Channel 0 */
#define TC1_ID		 7		/* Timer Channel 1 */
#define TC2_ID		 8		/* Timer Channel 2 */
#define TC3_ID		 9		/* Timer Channel 3 */
#define TC4_ID		10		/* Timer Channel 4 */
#define TC5_ID		11		/* Timer Channel 5 */

#define WD_ID		12		/* Watchdog interrupt */

#define PIOA_ID		13		/* Parallel I/O Controller A interrupt */
#define PIOB_ID		14		/* Parallel I/O Controller B interrupt */

#define ADC0_ID		15		/* Analog to Digital Converter Channel 0 interrupt */
#define ADC1_ID		16		/* Analog to Digital Converter Channel 1 interrupt */

#define DAC0_ID		17		/* Digital to Analog Converter Channel 0 interrupt */
#define DAC1_ID		18		/* Digital to Analog Converter Channel 1 interrupt */

#define RTC_ID		19		/* Real Time Clock interrupt */

#define APMC_ID		20		/* Advanced Power Management Controller interrupt */

#define SLCKIRQ_ID	23		/* Slow Clock Interrupt */
#define IRQ5_ID		24		/* External interrupt 5 */
#define IRQ4_ID		25		/* External interrupt 4 */
#define IRQ3_ID		26		/* External interrupt 3 */
#define IRQ2_ID		27		/* External interrupt 2 */
#define IRQ1_ID		28		/* External interrupt 1 */
#define IRQ0_ID		29		/* External interrupt 0 */

#define COMMRX_ID	30		/* RX Debug Communication Channel interrupt */
#define COMMTX_ID	31		/* TX Debug Communication Channel interrupt */

#define ISR_MIN		 0
#define ISR_MAX		31
#define ISR_COUNT	31

#define MAXIRQNUM	(ISR_MAX)
#define MAXFIQNUM	(MAXIRQNUM)
#define MAXSWINUM	15

#define NR_IRQS		(MAXIRQNUM + 1)


/*----------------------------------*/
/* External Bus Interface Registers */
/*----------------------------------*/
#define EBI_BASE	0xFFE00000
#define EBI_CSR0	__REG32(EBI_BASE + 0x000)	/* Chip Select Register 0 */
#define EBI_CSR1	__REG32(EBI_BASE + 0x004)	/* Chip Select Register 1 */
#define EBI_CSR2	__REG32(EBI_BASE + 0x008)	/* Chip Select Register 2 */
#define EBI_CSR3	__REG32(EBI_BASE + 0x00C)	/* Chip Select Register 3 */
#define EBI_CSR4	__REG32(EBI_BASE + 0x010)	/* Chip Select Register 4 */
#define EBI_CSR5	__REG32(EBI_BASE + 0x014)	/* Chip Select Register 5 */
#define EBI_CSR6	__REG32(EBI_BASE + 0x018)	/* Chip Select Register 6 */
#define EBI_CSR7	__REG32(EBI_BASE + 0x01C)	/* Chip Select Register 7 */
#define EBI_RCR		__REG32(EBI_BASE + 0x020)	/* Remap Control Register */
#define EBI_MCR		__REG32(EBI_BASE + 0x024)	/* Memory Control Register */

/*------------------------------------------------*/
/* Advanced Power Management Controller Registers */
/*------------------------------------------------*/
#define APMC_BASE	0xFFFF4000
#define APMC_SCER	__REG32(APMC_BASE + 0x000)	/* System Clock Enable  Register */
#define APMC_SCDR	__REG32(APMC_BASE + 0x004)	/* System Clock Disable Register */
#define APMC_SCSR	__REG32(APMC_BASE + 0x008)	/* System Clock Status  Register */
#define APMC_PCER	__REG32(APMC_BASE + 0x010)	/* Peripheral Clock Enable  Register */
#define APMC_PCDR	__REG32(APMC_BASE + 0x014)	/* Peripheral Clock Disable Register */
#define APMC_PCSR	__REG32(APMC_BASE + 0x018)	/* Peripheral Clock Status  Register */
#define APMC_CGMR	__REG32(APMC_BASE + 0x020)	/* Clock Generator Mode Register */
#define APMC_PCR	__REG32(APMC_BASE + 0x028)	/* Power Control Register */
#define APMC_PMR	__REG32(APMC_BASE + 0x02C)	/* Power Mode Register */
#define APMC_SR		__REG32(APMC_BASE + 0x030)	/* Status Register */
#define APMC_IER	__REG32(APMC_BASE + 0x034)	/* Interrupt Enable Register */
#define APMC_IDR	__REG32(APMC_BASE + 0x038)	/* Interrupt Disable Register */
#define APMC_IMR	__REG32(APMC_BASE + 0x03C)	/* Interrupt Mask Register */

/* Advanced Power Managment Control Register Bits Definition */
#define APMC_ARM7DIS		(1 << 0)		/* disable ARM7 core */

/* Advanced Power Managment Clock Generator Mode Register Bits Definition */
#define APMC_MOSC_BYP		(1 << 0)		/* Main Oscillator Bypass */
#define APMC_MOSC_EN		(1 << 1)		/* Main Oscillator Enable */
#define APMC_MCKO_DIS		(1 << 2)		/* Disable Master clock output (tri-state) */

#define APMC_PRES_NONE		0x0		/* No prescaler */
#define APMC_PRES_DIV2		0x10	/* Selected Clock Divided by 2 */
#define APMC_PRES_DIV4		0x20	/* Selected Clock Divided by 4 */
#define APMC_PRES_DIV8		0x30	/* Selected Clock Divided by 8 */
#define APMC_PRES_DIV16		0x40	/* Selected Clock Divided by 16 */
#define APMC_PRES_DIV32		0x50	/* Selected Clock Divided by 32 */
#define APMC_PRES_DIV64		0x60	/* Selected Clock Divided by 64 */

#define APMC_CSS_LF			0x0		/* Low-Frequency Clock provided by RTC */
#define APMC_CSS_MOSC		0x4000	/* Main Oscillator Output or External clock */
#define APMC_CSS_PLL		0x8000	/* Phase Lock Loop Output */

#define B_MUL				 8
#define B_OSCOUNT			16
#define B_PLLCOUNT			24

/* APM Power Controller Register Bits Definition */
#define APMC_SHDALC				0x1		/* Shut-down or alarm Command */
#define APMC_WKACKC				0x2		/* Wake-up or Alarm Acknowledge Command */

/* APM Power Mode Register Bits Definition */
#define APMC_SHDALS_OUT_TRIS	0x0		/* SHDALS pin is Tri-State */
#define APMC_SHDALS_OUT_LEVEL_0	0x1		/* SHDALS pin is LEVEL 0 */
#define APMC_SHDALS_OUT_LEVEL_1	0x2		/* SHDALS pin LEVEL 1 */

#define APMC_WKACKS_OUT_TRIS	0x0		/* WKACKS pin is Tri-State */
#define APMC_WKACKS_OUT_LEVEL_0	0x4		/* WKACKS pin  is LEVEL 0 */
#define APMC_WKACKS_OUT_LEVEL_1	0x8		/* WKACKS pin  is LEVEL 1 */

#define APMC_WKEN				(1 << 4)	/* Alarm Wake-up Enable */
#define APMC_ALSHEN				(1 << 5)	/* Alarm Shut-Down Enable */

#define APMC_WKEDG_NONE			0x00	/* None. No edge is detected on Wake-up */
#define APMC_WKEDG_POS_EDG		0x40	/* Positive edge detection */
#define APMC_WKEDG_NEG_EDG		0x80	/* Negative edge detection */

/* APM SR, IER, IDR and IMR Registers Bits Definition   */
#define APMC_MOSCS				(1 << 0)	/* Main Osillator Status */
#define APMC_PLL_LOCK			(1 << 1)	/* PLL Lock Status */

/*-----------------------------------------*/
/* Advanced Interrupt Controller Registers */
/*-----------------------------------------*/
#define AIC_BASE	0xFFFFF000
/* Source Mode Register */
#define AIC_SMR0	__REG32(AIC_BASE + 0x000)
#define AIC_SMR1	__REG32(AIC_BASE + 0x004)
#define AIC_SMR2	__REG32(AIC_BASE + 0x008)
#define AIC_SMR3	__REG32(AIC_BASE + 0x00C)
#define AIC_SMR4	__REG32(AIC_BASE + 0x010)
#define AIC_SMR5	__REG32(AIC_BASE + 0x014)
#define AIC_SMR6	__REG32(AIC_BASE + 0x018)
#define AIC_SMR7	__REG32(AIC_BASE + 0x01C)
#define AIC_SMR8	__REG32(AIC_BASE + 0x020)
#define AIC_SMR9	__REG32(AIC_BASE + 0x024)
#define AIC_SMR10	__REG32(AIC_BASE + 0x028)
#define AIC_SMR11	__REG32(AIC_BASE + 0x02C)
#define AIC_SMR12	__REG32(AIC_BASE + 0x030)
#define AIC_SMR13	__REG32(AIC_BASE + 0x034)
#define AIC_SMR14	__REG32(AIC_BASE + 0x038)
#define AIC_SMR15	__REG32(AIC_BASE + 0x03C)
#define AIC_SMR16	__REG32(AIC_BASE + 0x040)
#define AIC_SMR17	__REG32(AIC_BASE + 0x044)
#define AIC_SMR18	__REG32(AIC_BASE + 0x048)
#define AIC_SMR19	__REG32(AIC_BASE + 0x04C)
#define AIC_SMR20	__REG32(AIC_BASE + 0x050)
#define AIC_SMR21	__REG32(AIC_BASE + 0x054)
#define AIC_SMR22	__REG32(AIC_BASE + 0x058)
#define AIC_SMR23	__REG32(AIC_BASE + 0x05C)
#define AIC_SMR24	__REG32(AIC_BASE + 0x060)
#define AIC_SMR25	__REG32(AIC_BASE + 0x064)
#define AIC_SMR26	__REG32(AIC_BASE + 0x068)
#define AIC_SMR27	__REG32(AIC_BASE + 0x06C)
#define AIC_SMR28	__REG32(AIC_BASE + 0x070)
#define AIC_SMR29	__REG32(AIC_BASE + 0x074)
#define AIC_SMR30	__REG32(AIC_BASE + 0x078)
#define AIC_SMR31	__REG32(AIC_BASE + 0x07C)
/* Source Vector Register */
#define AIC_SVR0	__REG32(AIC_BASE + 0x080)
#define AIC_SVR1	__REG32(AIC_BASE + 0x084)
#define AIC_SVR2	__REG32(AIC_BASE + 0x088)
#define AIC_SVR3	__REG32(AIC_BASE + 0x08C)
#define AIC_SVR4	__REG32(AIC_BASE + 0x090)
#define AIC_SVR5	__REG32(AIC_BASE + 0x094)
#define AIC_SVR6	__REG32(AIC_BASE + 0x098)
#define AIC_SVR7	__REG32(AIC_BASE + 0x09C)
#define AIC_SVR8	__REG32(AIC_BASE + 0x0A0)
#define AIC_SVR9	__REG32(AIC_BASE + 0x0A4)
#define AIC_SVR10	__REG32(AIC_BASE + 0x0A8)
#define AIC_SVR11	__REG32(AIC_BASE + 0x0AC)
#define AIC_SVR12	__REG32(AIC_BASE + 0x0B0)
#define AIC_SVR13	__REG32(AIC_BASE + 0x0B4)
#define AIC_SVR14	__REG32(AIC_BASE + 0x0B8)
#define AIC_SVR15	__REG32(AIC_BASE + 0x0BC)
#define AIC_SVR16	__REG32(AIC_BASE + 0x0C0)
#define AIC_SVR17	__REG32(AIC_BASE + 0x0C4)
#define AIC_SVR18	__REG32(AIC_BASE + 0x0C8)
#define AIC_SVR19	__REG32(AIC_BASE + 0x0CC)
#define AIC_SVR20	__REG32(AIC_BASE + 0x0D0)
#define AIC_SVR21	__REG32(AIC_BASE + 0x0D4)
#define AIC_SVR22	__REG32(AIC_BASE + 0x0D8)
#define AIC_SVR23	__REG32(AIC_BASE + 0x0DC)
#define AIC_SVR24	__REG32(AIC_BASE + 0x0E0)
#define AIC_SVR25	__REG32(AIC_BASE + 0x0E4)
#define AIC_SVR26	__REG32(AIC_BASE + 0x0E8)
#define AIC_SVR27	__REG32(AIC_BASE + 0x0EC)
#define AIC_SVR28	__REG32(AIC_BASE + 0x0F0)
#define AIC_SVR29	__REG32(AIC_BASE + 0x0F4)
#define AIC_SVR30	__REG32(AIC_BASE + 0x0F8)
#define AIC_SVR31	__REG32(AIC_BASE + 0x0FC)


#define AIC_IVR		__REG32(AIC_BASE + 0x100)	/* IRQ Vector Register */
#define AIC_FVR		__REG32(AIC_BASE + 0x104)	/* FIQ Vector Register */
#define AIC_ISR		__REG32(AIC_BASE + 0x108)	/* Interrupt Status Register */
#define AIC_IPR		__REG32(AIC_BASE + 0x10C)	/* Interrupt Pending Register */
#define AIC_IMR		__REG32(AIC_BASE + 0x110)	/* Interrupt Mask Register */
#define AIC_CISR	__REG32(AIC_BASE + 0x114)	/* Core Interrupt Status Register */
#define AIC_IECR	__REG32(AIC_BASE + 0x120)	/* Interrupt Enable Command Register */
#define AIC_IDCR	__REG32(AIC_BASE + 0x124)	/* Interrupt Disable Command Register */
#define AIC_ICCR	__REG32(AIC_BASE + 0x128)	/* Interrupt Clear Command Register */
#define AIC_ISCR	__REG32(AIC_BASE + 0x12C)	/* Interrupt Set Command Register */
#define AIC_EOICR	__REG32(AIC_BASE + 0x130)	/* End of Interrupt Command Register */
#define AIC_SPU		__REG32(AIC_BASE + 0x134)	/* Spurious Vector Register */

/* AIC_SMR[]: Interrupt Source Mode Registers */
#define AIC_PRIOR						0x07	/* Priority */
#define AIC_PRIOR_LOWEST				0		/* lowest priority level */
#define AIC_PRIOR_HIGHEST				7		/* highest priority level */

#define AIC_SRCTYPE						0x60	/* Source Type Definition */

/* Internal Interrupts */
#define AIC_SRCTYPE_INT_LEVEL_SENSITIVE	0x00	/* Level Sensitive */
#define AIC_SRCTYPE_INT_EDGE_TRIGGERED	0x20	/* Edge Triggered */

/* External Interrupts */
#define AIC_SRCTYPE_EXT_LOW_LEVEL		0x00	/* Low Level */
#define AIC_SRCTYPE_EXT_NEGATIVE_EDGE	0x20	/* Negative Edge */
#define AIC_SRCTYPE_EXT_HIGH_LEVEL		0x40	/* High Level */
#define AIC_SRCTYPE_EXT_POSITIVE_EDGE	0x60	/* Positive Edge */

/* AIC_ISR: Interrupt Status Register */
#define AIC_IRQID						0x1F	/* Current source interrupt */

/* AIC_CISR: Interrupt Core Status Register */
#define AIC_NFIQ						0x01	/* Core FIQ Status */
#define AIC_NIRQ						0x02	/* Core IRQ Status */



/*---------------------------------------*/
/* User Interface Parallel I/O Interface */
/*---------------------------------------*/
/* Parallel I/O Controller A */
#define PIOA_BASE	0xFFFEC000
#define PIOA_PER	__REG32(PIOA_BASE + 0x00)	/* PIO Enable Register */
#define PIOA_PDR	__REG32(PIOA_BASE + 0x04)	/* PIO Disable Register */
#define PIOA_PSR	__REG32(PIOA_BASE + 0x08)	/* PIO Status Register */
#define PIOA_OER	__REG32(PIOA_BASE + 0x10)	/* Output Enable Register */
#define PIOA_ODR	__REG32(PIOA_BASE + 0x14)	/* Output Disable Register */
#define PIOA_OSR	__REG32(PIOA_BASE + 0x18)	/* Output Status Register */
#define PIOA_IFER	__REG32(PIOA_BASE + 0x20)	/* Input Filter Enable Register */
#define PIOA_IFDR	__REG32(PIOA_BASE + 0x24)	/* Input Filter Disable Register */
#define PIOA_IFSR	__REG32(PIOA_BASE + 0x28)	/* Input Filter Status Register */
#define PIOA_SODR	__REG32(PIOA_BASE + 0x30)	/* Set Output Data Register */
#define PIOA_CODR	__REG32(PIOA_BASE + 0x34)	/* Clear Output Data Register */
#define PIOA_ODSR	__REG32(PIOA_BASE + 0x38)	/* Output Data Status Register */
#define PIOA_PDSR	__REG32(PIOA_BASE + 0x3C)	/* Pin Data Status Register */
#define PIOA_IER	__REG32(PIOA_BASE + 0x40)	/* Interrupt Enable Register */
#define PIOA_IDR	__REG32(PIOA_BASE + 0x44)	/* Interrupt Disable Register */
#define PIOA_IMR	__REG32(PIOA_BASE + 0x48)	/* Interrupt Mask Register */
#define PIOA_ISR	__REG32(PIOA_BASE + 0x4C)	/* Interrupt Status Register */
#define PIOA_MDER	__REG32(PIOA_BASE + 0x50)	/* Multi Driver Enable Register */
#define PIOA_MDDR	__REG32(PIOA_BASE + 0x54)	/* Multi Driver Disable Register */
#define PIOA_MDSR	__REG32(PIOA_BASE + 0x58)	/* Multi Driver Status Register */

/* Parallel I/O Controller B */
#define PIOB_BASE	0xFFFF0000
#define PIOB_PER	__REG32(PIOB_BASE + 0x00)	/* PIO Enable Register */
#define PIOB_PDR	__REG32(PIOB_BASE + 0x04)	/* PIO Disable Register */
#define PIOB_PSR	__REG32(PIOB_BASE + 0x08)	/* PIO Status Register */
#define PIOB_OER	__REG32(PIOB_BASE + 0x10)	/* Output Enable Register */
#define PIOB_ODR	__REG32(PIOB_BASE + 0x14)	/* Output Disable Register */
#define PIOB_OSR	__REG32(PIOB_BASE + 0x18)	/* Output Status Register */
#define PIOB_IFER	__REG32(PIOB_BASE + 0x20)	/* Input Filter Enable Register */
#define PIOB_IFDR	__REG32(PIOB_BASE + 0x24)	/* Input Filter Disable Register */
#define PIOB_IFSR	__REG32(PIOB_BASE + 0x28)	/* Input Filter Status Register */
#define PIOB_SODR	__REG32(PIOB_BASE + 0x30)	/* Set Output Data Register */
#define PIOB_CODR	__REG32(PIOB_BASE + 0x34)	/* Clear Output Data Register */
#define PIOB_ODSR	__REG32(PIOB_BASE + 0x38)	/* Output Data Status Register */
#define PIOB_PDSR	__REG32(PIOB_BASE + 0x3C)	/* Pin Data Status Register */
#define PIOB_IER	__REG32(PIOB_BASE + 0x40)	/* Interrupt Enable Register */
#define PIOB_IDR	__REG32(PIOB_BASE + 0x44)	/* Interrupt Disable Register */
#define PIOB_IMR	__REG32(PIOB_BASE + 0x48)	/* Interrupt Mask Register */
#define PIOB_ISR	__REG32(PIOB_BASE + 0x4C)	/* Interrupt Status Register */
#define PIOB_MDER	__REG32(PIOB_BASE + 0x50)	/* Multi Driver Enable Register */
#define PIOB_MDDR	__REG32(PIOB_BASE + 0x54)	/* Multi Driver Disable Register */
#define PIOB_MDSR	__REG32(PIOB_BASE + 0x58)	/* Multi Driver Status Register */

/*------------------*/
/* Pin Multiplexing */
/*------------------*/
/* PIO Controller A */
#define NB_PIOA		30		/* Number of PIO A Lines */

#define PIOTCLK3	 0		/* Timer 3 Clock signal */
#define PIOTIOA3	 1		/* Timer 3 Signal A */
#define PIOTIOB3	 2		/* Timer 3 Signal B */

#define PIOTCLK4	 3		/* Timer 4 Clock signal */
#define PIOTIOA4	 4		/* Timer 4 Signal A */
#define PIOTIOB4	 5		/* Timer 4 Signal B */

#define PIOTCLK5	 6		/* Timer 5 Clock signal */
#define PIOTIOA5	 7		/* Timer 5 Signal A */
#define PIOTIOB5	 8		/* Timer 5 Signal B */

#define PIOIRQ0		 9		/* External Interrupt 0 */
#define PIOIRQ1		10		/* External Interrupt 1 */
#define PIOIRQ2		11		/* External Interrupt 2 */
#define PIOIRQ3		12		/* External Interrupt 3 */
#define PIOFIQ		13		/* Fast Interrupt */

#define PIOSCK0		14		/* USART 0 signal */
#define PIOTXD0		15		/* USART 0 transmit data */
#define PIORXD0		16		/* USART 0 receive data  */
#define PIOSCK1		17		/* USART 1 clock signal  */
#define PIOTXD1		18		/* USART 1 transmit data */
#define PIORXD1		19		/* USART 1 receive data  */
#define PIOSCK2		20		/* USART 2 signal */
#define PIOTXD2		21		/* USART 2 transmit data */
#define PIORXD2		22		/* USART 2 receive data  */

#define PIOSPCK		23		/* SPI clock signal */
#define PIOMISO		24		/* SPI Master In Slave */
#define PIOMOSI		25		/* SPI Master Out Slave */
#define PIONPCS0	26		/* SPI Peripheral Chip Select 0 */
#define PIONSS		PIONPCS0
#define PIONPCS1	27		/* SPI Peripheral Chip Select 1 */
#define PIONPCS2	28		/* SPI Peripheral Chip Select 2 */
#define PIONPCS3	29		/* SPI Peripheral Chip Select 3 */

/* PIO Controller B */
#define NB_PIOB		28		/* Number of PIO B Lines */

#define PIOIRQ4		 3		/* External Interrupt 4 */
#define PIOIRQ5		 4		/* External Interrupt 5 */
#define PIOSLCKIRQ	 5		/* External Interrupt 6 */

#define PIOAD0TRIG	 6		/* ADC 0 External trigger */
#define PIOAD1TRIG	 7		/* ADC 1 External trigger */

#define PIOBMS		18		/* Boot Mode Select */

#define PIOTCLK0	19		/* Timer 0 Clock signal input */
#define PIOTIOA0	20		/* Timer 0 Signal A   */
#define PIOTIOB0	21		/* Timer 0 Signal B   */

#define PIOTCLK1	22		/* Timer 1 Clock signal */
#define PIOTIOA1	23		/* Timer 1 Signal A */
#define PIOTIOB1	24		/* Timer 1 Signal B */

#define PIOTCLK2	25		/* Timer 2 Clock signal */
#define PIOTIOA2	26		/* Timer 2 Signal A */
#define PIOTIOB2	27		/* Timer 2 Signal B */


/*-----------------------------------*/
/* User Interface Timer/Counter Unit */
/*-----------------------------------*/

/* Timer Control Block 0: Channels 0, 1, 2 */
#define TCB0_BASE	0xFFFD0000
#define TC0_CCR		__REG32(TCB0_BASE + 0x00)	/* Control Register */
#define TC0_CMR		__REG32(TCB0_BASE + 0x04)	/* Mode Register */
#define TC0_CV		__REG32(TCB0_BASE + 0x10)	/* Counter value */
#define TC0_RA		__REG32(TCB0_BASE + 0x14)	/* Register A */
#define TC0_RB		__REG32(TCB0_BASE + 0x18)	/* Register B */
#define TC0_RC		__REG32(TCB0_BASE + 0x1C)	/* Register C */
#define TC0_SR		__REG32(TCB0_BASE + 0x20)	/* Status Register */
#define TC0_IER		__REG32(TCB0_BASE + 0x24)	/* Interrupt Enable Register */
#define TC0_IDR		__REG32(TCB0_BASE + 0x28)	/* Interrupt Disable Register */
#define TC0_IMR		__REG32(TCB0_BASE + 0x2C)	/* Interrupt Mask Register */

#define TC1_CCR		__REG32(TCB0_BASE + 0x40)	/* Control Register */
#define TC1_CMR		__REG32(TCB0_BASE + 0x44)	/* Mode Register */
#define TC1_CV		__REG32(TCB0_BASE + 0x50)	/* Counter value */
#define TC1_RA		__REG32(TCB0_BASE + 0x54)	/* Register A */
#define TC1_RB		__REG32(TCB0_BASE + 0x58)	/* Register B */
#define TC1_RC		__REG32(TCB0_BASE + 0x5C)	/* Register C */
#define TC1_SR		__REG32(TCB0_BASE + 0x60)	/* Status Register */
#define TC1_IER		__REG32(TCB0_BASE + 0x64)	/* Interrupt Enable Register */
#define TC1_IDR		__REG32(TCB0_BASE + 0x68)	/* Interrupt Disable Register */
#define TC1_IMR		__REG32(TCB0_BASE + 0x6C)	/* Interrupt Mask Register */

#define TC2_CCR		__REG32(TCB0_BASE + 0x80)	/* Control Register */
#define TC2_CMR		__REG32(TCB0_BASE + 0x84)	/* Mode Register */
#define TC2_CV		__REG32(TCB0_BASE + 0x90)	/* Counter value */
#define TC2_RA		__REG32(TCB0_BASE + 0x94)	/* Register A */
#define TC2_RB		__REG32(TCB0_BASE + 0x98)	/* Register B */
#define TC2_RC		__REG32(TCB0_BASE + 0x9C)	/* Register C */
#define TC2_SR		__REG32(TCB0_BASE + 0xA0)	/* Status Register */
#define TC2_IER		__REG32(TCB0_BASE + 0xA4)	/* Interrupt Enable Register */
#define TC2_IDR		__REG32(TCB0_BASE + 0xA8)	/* Interrupt Disable Register */
#define TC2_IMR		__REG32(TCB0_BASE + 0xAC)	/* Interrupt Mask Register */

#define TCB0_BCR	__REG32(TCB0_BASE + 0xC0)	/* Block Control Register */
#define TCB0_BMR	__REG32(TCB0_BASE + 0xC4)	/* Block Mode Register  */

/* Timer Control Block 1: Channels 3, 4, 5 */
#define TCB1_BASE	0xFFFD4000
#define TC3_CCR		__REG32(TCB1_BASE + 0x00)	/* Control Register */
#define TC3_CMR		__REG32(TCB1_BASE + 0x04)	/* Mode Register */
#define TC3_CV		__REG32(TCB1_BASE + 0x10)	/* Counter value */
#define TC3_RA		__REG32(TCB1_BASE + 0x14)	/* Register A */
#define TC3_RB		__REG32(TCB1_BASE + 0x18)	/* Register B */
#define TC3_RC		__REG32(TCB1_BASE + 0x1C)	/* Register C */
#define TC3_SR		__REG32(TCB1_BASE + 0x20)	/* Status Register */
#define TC3_IER		__REG32(TCB1_BASE + 0x24)	/* Interrupt Enable Register */
#define TC3_IDR		__REG32(TCB1_BASE + 0x28)	/* Interrupt Disable Register */
#define TC3_IMR		__REG32(TCB1_BASE + 0x2C)	/* Interrupt Mask Register */

#define TC4_CCR		__REG32(TCB1_BASE + 0x40)	/* Control Register */
#define TC4_CMR		__REG32(TCB1_BASE + 0x44)	/* Mode Register */
#define TC4_CV		__REG32(TCB1_BASE + 0x50)	/* Counter value */
#define TC4_RA		__REG32(TCB1_BASE + 0x54)	/* Register A */
#define TC4_RB		__REG32(TCB1_BASE + 0x58)	/* Register B */
#define TC4_RC		__REG32(TCB1_BASE + 0x5C)	/* Register C */
#define TC4_SR		__REG32(TCB1_BASE + 0x60)	/* Status Register */
#define TC4_IER		__REG32(TCB1_BASE + 0x64)	/* Interrupt Enable Register */
#define TC4_IDR		__REG32(TCB1_BASE + 0x68)	/* Interrupt Disable Register */
#define TC4_IMR		__REG32(TCB1_BASE + 0x6C)	/* Interrupt Mask Register */

#define TC5_CCR		__REG32(TCB1_BASE + 0x80)	/* Control Register */
#define TC5_CMR		__REG32(TCB1_BASE + 0x84)	/* Mode Register */
#define TC5_CV		__REG32(TCB1_BASE + 0x90)	/* Counter value */
#define TC5_RA		__REG32(TCB1_BASE + 0x94)	/* Register A */
#define TC5_RB		__REG32(TCB1_BASE + 0x98)	/* Register B */
#define TC5_RC		__REG32(TCB1_BASE + 0x9C)	/* Register C */
#define TC5_SR		__REG32(TCB1_BASE + 0xA0)	/* Status Register */
#define TC5_IER		__REG32(TCB1_BASE + 0xA4)	/* Interrupt Enable Register */
#define TC5_IDR		__REG32(TCB1_BASE + 0xA8)	/* Interrupt Disable Register */
#define TC5_IMR		__REG32(TCB1_BASE + 0xAC)	/* Interrupt Mask Register */

#define TCB1_BCR	__REG32(TCB1_BASE + 0xC0)	/* Block Control Register */
#define TCB1_BMR	__REG32(TCB1_BASE + 0xC4)	/* Block Mode Register  */


/* TC_CCR: Timer Counter Control Register Bits Definition */
#define TC_CLKEN			(1 << 0)	/* Counter Clock Enable */
#define TC_CLKDIS			(1 << 1)	/* Counter Clock Disable */
#define TC_SWTRG			(1 << 2)	/* Software Trigger */

/* TC_CMR: Timer Counter Channel Mode Register Bits Definition */

/* Clock Selection */
#define TC_CLKS				(0x07 << 0)
#define TC_CLKS_MCK2		(0x00 << 0)
#define TC_CLKS_MCK8		(0x01 << 0)
#define TC_CLKS_MCK32		(0x02 << 0)
#define TC_CLKS_MCK128		(0x03 << 0)
#define TC_CLKS_MCK1024		(0x04 << 0)

#define TC_CLKS_SLCK		(0x04 << 0)

#define TC_CLKS_XC0			(0x05 << 0)
#define TC_CLKS_XC1			(0x06 << 0)
#define TC_CLKS_XC2			(0x07 << 0)


/* Clock Inversion */
#define TC_CLKI				(1 << 3)

/* Burst Signal Selection */
#define TC_BURST			(0x03 << 4)
#define TC_BURST_NONE		(0x00 << 4)
#define TC_BUSRT_XC0		(0x01 << 4)
#define TC_BURST_XC1		(0x02 << 4)
#define TC_BURST_XC2		(0x03 << 4)

/* Capture Mode : Counter Clock Stopped with RB Loading */
#define TC_LDBSTOP			(1 << 6)

/* Waveform Mode : Counter Clock Stopped with RC Compare */
#define TC_CPCSTOP			(1 << 6)

/* Capture Mode : Counter Clock Disabled with RB Loading */
#define TC_LDBDIS			(1 << 7)

/* Waveform Mode : Counter Clock Disabled with RC Compare */
#define TC_CPCDIS			(1 << 7)

/* Capture Mode : External Trigger Edge Selection */
#define TC_ETRGEDG			(0x03 << 8)
#define TC_ETRGEDG_NONE		(0x00 << 8)
#define TC_ETRGEDG_RISING	(0x01 << 8)
#define TC_ETRGEDG_FALLING	(0x02 << 8)
#define TC_ETRGEDG_BOTH		(0x03 << 8)

/* Waveform Mode : External Event Edge Selection */
#define TC_EEVTEDG			(0x03 << 8)
#define TC_EEVTEDG_NONE		(0x00 << 8)
#define TC_EEVTEDG_RISING	(0x01 << 8)
#define TC_EEVTEDG_FALLING	(0x02 << 8)
#define TC_EEVTEDG_BOTH		(0x03 << 8)

/* Capture Mode : TIOA or TIOB External Trigger Selection */
#define TC_ABETRG			(0x01 << 10)
#define TC_ABETRG_TIOB		(0x00 << 10)
#define TC_ABETRG_TIOA		(0x01 << 10)

/* Waveform Mode : External Event Selection */
#define TC_EEVT				(0x03 << 10)
#define TC_EEVT_TIOB		(0x00 << 10)
#define TC_EEVT_XC0			(0x01 << 10)
#define TC_EEVT_XC1			(0x02 << 10)
#define TC_EEVT_XC2			(0x03 << 10)

#define TC_ENETRG			(1 << 12)	/* Waveform Mode : External Event Trigger enable */

#define TC_CPCTRG			(1 << 14)	/* RC Compare Trigger Enable */

/* Mode Selection */
#define TC_WAVE				(1 << 15)
#define TC_CAPT				(0 << 15)

/* Capture Mode : RA Loading Selection */
#define TC_LDRA				(0x03 << 16)
#define TC_LDRA_NONE		(0x00 << 16)
#define TC_LDRA_RISING		(0x01 << 16)
#define TC_LDRA_FALLING		(0x02 << 16)
#define TC_LDRA_BOTH		(0x03 << 16)

/* Waveform Mode : RA Compare Effect on TIOA */
#define TC_ACPA				(0x03 << 16)
#define TC_ACPA_NONE		(0x00 << 16)
#define TC_ACPA_SET			(0x01 << 16)
#define TC_ACPA_CLEAR		(0x02 << 16)
#define TC_ACPA_TOGGLE		(0x03 << 16)

/* Capture Mode : RB Loading Selection */
#define TC_LDRB				(0x03 << 18)
#define TC_LDRB_NONE		(0x00 << 18)
#define TC_LDRB_RISING		(0x01 << 18)
#define TC_LDRB_FALLING		(0x02 << 18)
#define TC_LDRB_BOTH		(0x03 << 18)

/* Waveform Mode : RC Compare Effect on TIOA */
#define TC_ACPC				(0x03 << 18)
#define TC_ACPC_NONE		(0x00 << 18)
#define TC_ACPC_SET			(0x01 << 18)
#define TC_ACPC_CLEAR		(0x02 << 18)
#define TC_ACPC_TOGGLE		(0x03 << 18)

/* Waveform Mode : External Event Effect on TIOA */
#define TC_AEEVT			(0x03 << 20)
#define TC_AEEVT_NONE		(0x00 << 20)
#define TC_AEEVT_SET		(0x01 << 20)
#define TC_AEEVT_CLEAR		(0x02 << 20)
#define TC_AEEVT_TOGGLE		(0x03 << 20)

/* Waveform Mode : Software Trigger Effect on TIOA */
#define TC_ASWTRG			(0x03 << 22)
#define TC_ASWTRG_NONE		(0x00 << 22)
#define TC_ASWTRG_SET		(0x01 << 22)
#define TC_ASWTRG_CLEAR		(0x02 << 22)
#define TC_ASWTRG_TOGGLE	(0x03 << 22)

/* Waveform Mode : RB Compare Effect on TIOB */
#define TC_BCPB				(0x01 << 24)
#define TC_BCPB_NONE		(0x00 << 24)
#define TC_BCPB_SET			(0x01 << 24)
#define TC_BCPB_CLEAR		(0x02 << 24)
#define TC_BCPB_TOGGLE		(0x03 << 24)

/* Waveform Mode : RC Compare Effect on TIOB */
#define TC_BCPC				(0x03 << 26)
#define TC_BCPC_NONE		(0x00 << 26)
#define TC_BCPC_SET			(0x01 << 26)
#define TC_BCPC_CLEAR		(0x02 << 26)
#define TC_BCPC_TOGGLE		(0x03 << 26)

/* Waveform Mode : External Event Effect on TIOB */
#define TC_BEEVT			(0x03 << 28)
#define TC_BEEVT_NONE		(0x00 << 28)
#define TC_BEEVT_SET		(0x01 << 28)
#define TC_BEEVT_CLEAR		(0x02 << 28)
#define TC_BEEVT_TOGGLE		(0x03 << 28)

/* Waveform Mode : Software Trigger Effect on TIOB */
#define TC_BSWTRG			(0x03 << 30)
#define TC_BSWTRG_NONE		(0x00 << 30)
#define TC_BSWTRG_SET		(0x01 << 30)
#define TC_BSWTRG_CLEAR		(0x02 << 30)
#define TC_BSWTRG_TOGGLE	(0x03 << 30)

/* TC_SR: Timer Counter Status Register Bits Definition */
#define TC_COVFS			(1 <<  0)	/* Counter Overflow Status */
#define TC_LOVRS			(1 <<  1)	/* Load Overrun Status */
#define TC_CPAS				(1 <<  2)	/* RA Compare Status */
#define TC_CPBS				(1 <<  3)	/* RB Compare Status */
#define TC_CPCS				(1 <<  4)	/* RC Compare Status */
#define TC_LDRAS			(1 <<  5)	/* RA Loading Status */
#define TC_LDRBS			(1 <<  6)	/* RB Loading Status */
#define TC_ETRGS			(1 <<  7)	/* External Trigger Status */
#define TC_CLKSTA			(1 << 16)	/* Clock Status */
#define TC_MTIOA			(1 << 17)	/* TIOA Mirror */
#define TC_MTIOB			(1 << 18)	/* TIOB Mirror */

/* TC_BCR: Timer Counter Block Control Register Bits Definition */
#define TC_SYNC				(1 << 0)	/* Synchronisation Trigger */

/*  TC_BMR: Timer Counter Block Mode Register Bits Definition */
#define TC_TC0XC0S			(0x03 << 0)	/* External Clock Signal 0 Selection */
#define TC_TC0XC0S_TCLK0	(0x00 << 0)	/* TCLK0 connected to XC0 */
#define TC_TC0XC0S_NONE		(0x01 << 0)	/* No signal connected to XC0 */
#define TC_TC0XC0S_TIOA1	(0x02 << 0)	/* TIOA1 connected to XC0 */
#define TC_TC0XC0S_TIOA2	(0x03 << 0)	/* TIOA2 connected to XC0 */

#define TC_TC1XC1S			(0x03 << 2)	/* External Clock Signal 1 Selection */
#define TC_TC1XC1S_TCLK1	(0x00 << 2)	/* TCLK1 connected to XC1 */
#define TC_TC1XC1S_NONE		(0x01 << 2)	/* No signal connected to XC1 */
#define TC_TC1XC1S_TIOA0	(0x02 << 2)	/* TIOA0 connected to XC1 */
#define TC_TC1XC1S_TIOA2	(0x03 << 2)	/* TIOA2 connected to XC1 */

#define TC_TC2XC2S			(0x03 << 4)	/* External Clock Signal 2 Selection */
#define TC_TC2XC2S_TCLK2	(0x00 << 4)	/* TCLK2 connected to XC2 */
#define TC_TC2XC2S_NONE		(0x01 << 4)	/* No signal connected to XC2 */
#define TC_TC2XC2S_TIOA0	(0x02 << 4)	/* TIOA0 connected to XC2 */
#define TC_TC2XC2S_TIOA1	(0x03 << 4)	/* TIOA1 connected to XC2 */



/*----------------------*/
/* User Interface USART */
/*----------------------*/

/* USART 0 */
#define USART0_BASE	0xFFFC0000
#define US0_CR		__REG32(USART0_BASE + 0x000)	/* Control Register */
#define US0_MR		__REG32(USART0_BASE + 0x004)	/* Mode Register */
#define US0_IER		__REG32(USART0_BASE + 0x008)	/* Interrupt Enable Register */
#define US0_IDR		__REG32(USART0_BASE + 0x00C)	/* Interrupt Disable Register */
#define US0_IMR		__REG32(USART0_BASE + 0x010)	/* Interrupt Mask Register */
#define US0_CSR		__REG32(USART0_BASE + 0x014)	/* Channel Status Register */
#define US0_RHR		__REG32(USART0_BASE + 0x018)	/* Receive Holding Register */
#define US0_THR		__REG32(USART0_BASE + 0x01C)	/* Transmit Holding Register */
#define US0_BRGR	__REG32(USART0_BASE + 0x020)	/* Baud Rate Generator Register */
#define US0_RTOR	__REG32(USART0_BASE + 0x024)	/* Receiver Timeout Register */
#define US0_TTGR	__REG32(USART0_BASE + 0x028)	/* Transmitter Time-guard Register */
#define US0_RPR		__REG32(USART0_BASE + 0x030)	/* Receiver Pointer Register */
#define US0_RCR		__REG32(USART0_BASE + 0x034)	/* Receiver Counter Register */
#define US0_TPR		__REG32(USART0_BASE + 0x038)	/* Transmitter Pointer Register */
#define US0_TCR		__REG32(USART0_BASE + 0x03C)	/* Transmitter Counter Register */

/* USART 1 */
#define USART1_BASE	0xFFFC4000
#define US1_CR		__REG32(USART1_BASE + 0x000)	/* Control Register */
#define US1_MR		__REG32(USART1_BASE + 0x004)	/* Mode Register */
#define US1_IER		__REG32(USART1_BASE + 0x008)	/* Interrupt Enable Register */
#define US1_IDR		__REG32(USART1_BASE + 0x00C)	/* Interrupt Disable Register */
#define US1_IMR		__REG32(USART1_BASE + 0x010)	/* Interrupt Mask Register */
#define US1_CSR		__REG32(USART1_BASE + 0x014)	/* Channel Status Register */
#define US1_RHR		__REG32(USART1_BASE + 0x018)	/* Receive Holding Register */
#define US1_THR		__REG32(USART1_BASE + 0x01C)	/* Transmit Holding Register */
#define US1_BRGR	__REG32(USART1_BASE + 0x020)	/* Baud Rate Generator Register */
#define US1_RTOR	__REG32(USART1_BASE + 0x024)	/* Receiver Timeout Register */
#define US1_TTGR	__REG32(USART1_BASE + 0x028)	/* Transmitter Time-guard Register */
#define US1_RPR		__REG32(USART1_BASE + 0x030)	/* Receiver Pointer Register */
#define US1_RCR		__REG32(USART1_BASE + 0x034)	/* Receiver Counter Register */
#define US1_TPR		__REG32(USART1_BASE + 0x038)	/* Transmitter Pointer Register */
#define US1_TCR		__REG32(USART1_BASE + 0x03C)	/* Transmitter Counter Register */

/* USART 2 */
#define USART2_BASE	0xFFFC8000
#define US2_CR		__REG32(USART2_BASE + 0x000)	/* Control Register */
#define US2_MR		__REG32(USART2_BASE + 0x004)	/* Mode Register */
#define US2_IER		__REG32(USART2_BASE + 0x008)	/* Interrupt Enable Register */
#define US2_IDR		__REG32(USART2_BASE + 0x00C)	/* Interrupt Disable Register */
#define US2_IMR		__REG32(USART2_BASE + 0x010)	/* Interrupt Mask Register */
#define US2_CSR		__REG32(USART2_BASE + 0x014)	/* Channel Status Register */
#define US2_RHR		__REG32(USART2_BASE + 0x018)	/* Receive Holding Register */
#define US2_THR		__REG32(USART2_BASE + 0x01C)	/* Transmit Holding Register */
#define US2_BRGR	__REG32(USART2_BASE + 0x020)	/* Baud Rate Generator Register */
#define US2_RTOR	__REG32(USART2_BASE + 0x024)	/* Receiver Timeout Register */
#define US2_TTGR	__REG32(USART2_BASE + 0x028)	/* Transmitter Time-guard Register */
#define US2_RPR		__REG32(USART2_BASE + 0x030)	/* Receiver Pointer Register */
#define US2_RCR		__REG32(USART2_BASE + 0x034)	/* Receiver Counter Register */
#define US2_TPR		__REG32(USART2_BASE + 0x038)	/* Transmitter Pointer Register */
#define US2_TCR		__REG32(USART2_BASE + 0x03C)	/* Transmitter Counter Register */


/* US_CR : Control Register */

#define US_RSTRX			(1 <<  2)	/* Reset Receiver */
#define US_RSTTX			(1 <<  3)	/* Reset Transmitter */
#define US_RXEN				(1 <<  4)	/* Receiver Enable */
#define US_RXDIS			(1 <<  5)	/* Receiver Disable */
#define US_TXEN				(1 <<  6)	/* Transmitter Enable */
#define US_TXDIS			(1 <<  7)	/* Transmitter Disable */
#define US_RSTSTA			(1 <<  8)	/* Reset Status Bits */
#define US_STTBRK			(1 <<  9)	/* Start Break */
#define US_STPBRK			(1 << 10)	/* Stop Break */
#define US_STTTO			(1 << 11)	/* Start Time-out */
#define US_SENDA			(1 << 12)	/* Send Address */

/* US_MR : Mode Register */

#define US_CLKS				(0x03 << 4)	/* Clock Selection */
#define US_CLKS_MCK			(0x00 << 4)	/* Master Clock */
#define US_CLKS_MCK8		(0x01 << 4)	/* Master Clock divided by 8 */
#define US_CLKS_SCK			(0x02 << 4)	/* External Clock */
#define US_CLKS_SLCK		(0x03 << 4)	/* Slow Clock */

#define US_CHRL				(0x03 << 6)	/* Byte Length */
#define US_CHRL_5			(0x00 << 6)	/* 5 bits */
#define US_CHRL_6			(0x01 << 6)	/* 6 bits */
#define US_CHRL_7			(0x02 << 6)	/* 7 bits */
#define US_CHRL_8			(0x03 << 6)	/* 8 bits */

#define US_SYNC				(1 << 8)	/* Synchronous Mode Enable */

#define US_PAR				(0x07 << 9)		/* Parity Mode */
#define US_PAR_EVEN			(0x00 << 9)		/* Even Parity */
#define US_PAR_ODD			(0x01 << 9)		/* Odd Parity */
#define US_PAR_SPACE		(0x02 << 9)		/* Space Parity to 0 */
#define US_PAR_MARK			(0x03 << 9)		/* Marked Parity to 1 */
#define US_PAR_NO			(0x04 << 9)		/* No Parity */
#define US_PAR_MULTIDROP	(0x06 << 9)		/* Multi-drop Mode */

#define US_NBSTOP			(0x03 << 12)	/* Stop Bit Number */
#define US_NBSTOP_1			(0x00 << 12)	/* 1 Stop Bit */
#define US_NBSTOP_1_5		(0x01 << 12)	/* 1.5 Stop Bits */
#define US_NBSTOP_2			(0x02 << 12)	/* 2 Stop Bits */

#define US_CHMODE					(0x03 << 14)	/* Channel Mode */
#define US_CHMODE_NORMAL			(0x00 << 14)	/* Normal Mode */
#define US_CHMODE_AUTOMATIC_ECHO	(0x01 << 14)	/* Automatic Echo */
#define US_CHMODE_LOCAL_LOOPBACK	(0x02 << 14)	/* Local Loopback */
#define US_CHMODE_REMOTE_LOOPBACK	(0x03 << 14)	/* Remote Loopback */

#define US_MODE9			(1 << 17)		/* 9 Bit Mode */
#define US_CLKO				(1 << 18)		/* Baud Rate Output Enable */

/* Mode Register model */

/* Standard Asynchronous Mode : 8 bits , 1 stop , no parity */
#define US_ASYNC_MODE		( US_CHMODE_NORMAL + \
							  US_NBSTOP_1 + \
							  US_PAR_NO + \
							  US_CHRL_8 + \
							  US_CLKS_MCK )

/* Standard External Asynchronous Mode : 8 bits , 1 stop , no parity */
#define US_ASYNC_SCK_MODE	( US_CHMODE_NORMAL + \
							  US_NBSTOP_1 + \
							  US_PAR_NO + \
							  US_CHRL_8 + \
							  US_CLKS_SCK )

/* Standard Synchronous Mode : 8 bits , 1 stop , no parity */
#define US_SYNC_MODE		( US_SYNC + \
							  US_CHMODE_NORMAL + \
							  US_NBSTOP_1 + \
							  US_PAR_NO + \
							  US_CHRL_8 + \
							  US_CLKS_MCK )

/* SCK used Label */
#define SCK_USED			(US_CLKO | US_CLKS_SCK)

/*---------------------------------------------------------------*/
/* US_IER, US_IDR, US_IMR, US_CSR: Status and Interrupt Register */
/*---------------------------------------------------------------*/

#define US_RXRDY			(1 <<  0)	/* Receiver Ready */
#define US_TXRDY			(1 <<  1)	/* Transmitter Ready */
#define US_RXBRK			(1 <<  2)	/* Receiver Break */
#define US_ENDRX			(1 <<  3)	/* End of Receiver PDC Transfer */
#define US_ENDTX			(1 <<  4)	/* End of Transmitter PDC Transfer */
#define US_OVRE				(1 <<  5)	/* Overrun Error */
#define US_FRAME			(1 <<  6)	/* Framing Error */
#define US_PARE				(1 <<  7)	/* Parity Error */
#define US_TIMEOUT			(1 <<  8)	/* Receiver Timeout */
#define US_TXEMPTY			(1 <<  9)	/* Transmitter Empty */

#define US_MASK_IRQ_TX		(US_TXRDY | US_ENDTX | US_TXEMPTY)
#define US_MASK_IRQ_RX		(US_RXRDY | US_ENDRX | US_TIMEOUT)
#define US_MASK_IRQ_ERROR	(US_PARE | US_FRAME | US_OVRE | US_RXBRK)




/*----------------------------*/
/* Real Time Clock Registers  */
/*----------------------------*/
#define RTC_BASE	0xFFFB8000

#define RTC_CR		__REG32(RTC_BASE + 0x000)	/* Control Register */
#define RTC_MR		__REG32(RTC_BASE + 0x004)	/* Mode Register */
#define RTC_TIMR	__REG32(RTC_BASE + 0x008)	/* Time Register */
#define RTC_CALR	__REG32(RTC_BASE + 0x00C)	/* Calendar Register */
#define RTC_TAR		__REG32(RTC_BASE + 0x010)	/* Time Alarm Register */
#define RTC_CAR		__REG32(RTC_BASE + 0x014)	/* Calendar Alarm Register */
#define RTC_SR		__REG32(RTC_BASE + 0x018)	/* Status Register */
#define RTC_SCCR	__REG32(RTC_BASE + 0x01C)	/* Status Clear Register */
#define RTC_IER		__REG32(RTC_BASE + 0x020)	/* Interrupt Enable Register */
#define RTC_IDR		__REG32(RTC_BASE + 0x024)	/* Interrupt Disable Register */
#define RTC_IMR		__REG32(RTC_BASE + 0x028)	/* Interrupt Mask Register */
#define RTC_VER		__REG32(RTC_BASE + 0x02C)	/* Valid Entry Register */

/* RTC_CR: Control Register bits definition  */
#define RTC_UPDTIM				0x00000001	/* Update Request Time Register */
#define RTC_UPDCAL				0x00000002	/* Update Request Calendar Register */
#define RTC_UPD_MASQ			0x00000003	/* Update Request masque */

#define RTC_TEVSEL_MN_CHG		0x00000000	/* Time Event Selection : Minute change */
#define RTC_TEVSEL_HR_CHG		0x00000100	/* Time Event Selection : Hour change */
#define RTC_TEVSEL_EVDAY_MD		0x00000200	/* Time Event Selection : Every Day at Midnight */
#define RTC_TEVSEL_EVDAY_NOON	0x00000300	/* Time Event Selection : Every Day at Noon */
#define RTC_TEVSEL_MASQ         0x00000300	/* Update TEVSEL masque */

#define RTC_CEVSEL_WEEK_CHG		0x00000000	/* Calendar Event Selection : Week Change (every Monday @ 00:00:00) */
#define RTC_CEVSEL_MONTH_CHG	0x00010000	/* Calendar Event Selection : Month Change (every 01 of each month @ 00:00:00) */
#define RTC_CEVSEL_YEAR_CHG		0x00020000	/* Calendar Event Selection : Year Change (every january 1st @ 00:00:00) */
#define RTC_CEVSEL_MASQ			0x00030000	/* Update CEVSEL masque */

/* RTC_MR: Mode Register bits definition */
#define RTC_24_HRMOD		0x0			/* 24-Hour Mode is selected */
#define RTC_12_HRMOD		0x1			/* 12-Hour Mode is selected */

/* RTC_TIMR: Time Register bits definition */
#define RTC_AM			(0 << 22)		/* AM Indicator */
#define RTC_PM			(1 << 22)		/* PM Indicator */
#define RTC_MASQ_SEC	(0x7F << 0)
#define RTC_MIN			8
#define RTC_MASQ_MIN	(0x7F << 8)
#define RTC_HOUR		16
#define RTC_MASQ_HOUR	(0x3F << 16)
#define RTC_AMPM		22
#define RTC_MASQ_AMPM	(0x01 << 22)

/* RTC_CALR:  Calendar Register bits definition     */
#define RTC_CENT		0
#define RTC_MASQ_CENT	(0x3F << 0)
#define RTC_YEAR		8
#define RTC_MASQ_YEAR	(0xFF << 8)
#define RTC_MONTH		16
#define RTC_MASQ_MONTH	(0x1F << 16)
#define RTC_DAY			21
#define RTC_MASQ_DAY	(0x07 << 21)
#define RTC_DATE		24
#define RTC_MASQ_DATE	(0x3F << 24)


/* RTC_TAR: Time Alarm Register bits definition */
#define RTC_SEC_ALRM_DIS	(0 << 7)	/* Second Alarm Disable */
#define RTC_SEC_ALRM_EN		(1 << 7)	/* Second Alarm Enable */

#define RTC_MIN_ALRM_DIS	(0 << 15)	/* Minute Alarm Disable */
#define RTC_MIN_ALRM_EN		(1 << 15)	/* Minute Alarm Enable */

#define RTC_HOUR_ALRM_DIS	(0 << 23)	/* Hour Alarm Disable */
#define RTC_HOUR_ALRM_EN	(1 << 23)	/* Hour Alarm Enable */

/* RTC_CAR: Calendar Alarm Register bits definition */
#define RTC_MONTH_ALRM_DIS	(0 << 23)	/* Month Alarm Disable */
#define RTC_MONTH_ALRM_EN	(1 << 23)	/* Month Alarm Enable */

#define RTC_DATE_ALRM_DIS	(0 << 31)	/* Date Alarm Disable */
#define RTC_DATE_ALRM_EN	(1 << 31)	/* Date Alarm Enable */

/* RTC Status, Interrupt Clear, Enable,Disable and Mask Register bits definition */
#define RTC_ACKUPD			(1 << 0)	/* Acknowledge for Update */
#define RTC_ALARM			(1 << 1)	/* Alarm Flag */
#define RTC_SEC				(1 << 2)	/* Second Event */
#define RTC_TIMEV			(1 << 3)	/* Time Event */
#define RTC_CALEV			(1 << 4)	/* Calendar Event */

/* RTC_VER:Valid Entry Register bits definition */
#define RTC_NVT				(1 << 0)	/* Non-Valid Time */
#define RTC_NVC				(1 << 1)	/* Non-Valid Calendar */
#define RTC_NVTAL			(1 << 2)	/* Non-Valid Time Alarm */
#define RTC_NVCAL			(1 << 3)	/* Non-Valid Calendar Alarm */



/*------------------------------------*/
/* Chip Identification User Interface */
/*------------------------------------*/
/* Special Function Base Address */
#define SF_BASE		0xFFF00000
#define SF_CIDR		__REG32(SF_BASE + 0x000)	/* Chip ID Register */
#define SF_EXID		__REG32(SF_BASE + 0x004)	/* Chip ID Extension Register */
#define SF_RSR		__REG32(SF_BASE + 0x008)	/* Reset Status Register */
#define SF_PMR		__REG32(SF_BASE + 0x018)	/* Protect Mode Register */

/* SF_CIDR: Chip Identification Register Bits Definition */

#define SF_VERSION		0x1F			/* Version Number */
#define SF_NVPSIZ		(0x0F << 8)		/* Non Volatile Program Memory Size */
#define SF_NVDSIZ		(0x0F << 12)	/* Non Volatile Data Memory Size */
#define SF_VDSIZ		(0x0F << 16)	/* Volatile Data Memory Size */
#define SF_ARCH			(0xFF << 20)	/* Architecture Code */
#define SF_NVPTYP		(0x07 << 28)	/* Non Volatile Program Memory Type */
#define SF_EXT			(0x1U << 31)	/* Extension Flag */


/* SF_ARCH: */
#define SF_ARCH_AT91x63		(0x63 << 20)
#define SF_ARCH_AT91x40		(0x40 << 20)
#define SF_ARCH_AT91x55		(0x55 << 20)

/* SF_NVPSIZ */
#define SF_NVPSIZ_NONE		(0 << 8)
#define SF_NVPSIZ_32K		(3 << 8)
#define SF_NVPSIZ_64K		(5 << 8)
#define SF_NVPSIZ_128K		(7 << 8)
#define SF_NVPSIZ_256K		(9 << 8)

/* SF_NVDSIZ */
#define SF_NVDSIZ_NONE		0
/* SF_VDSIZ */
#define SF_VDSIZ_NONE		(0 << 16)
#define SF_VDSIZ_1K			(1 << 16)
#define SF_VDSIZ_2K			(2 << 16)
#define SF_VDSIZ_4K			(4 << 16)
#define SF_VDSIZ_8K			(8 << 16)

/* SF_NVPTYP */
#define SF_NVPTYP_M			(1 << 28)	/* M or F series */
#define SF_NVPTYP_C			(2 << 28)	/* C series */
#define SF_NVPTYP_S			(3 << 28)	/* S series */
#define SF_NVPTYP_R			(4 << 28)	/* R series */

/* SF_RSR: Reset Status Register Definition */

#define SF_EXT_RESET		0x6C	/* External pin Cause Reset */
#define SF_WD_RESET			0x53	/* Internal WatchDog Cause Reset */

/* SF_PMR: Protect Control Register */

#define SF_AIC				(0x1 << 5)



/*-----------------------*/
/* DAC 0 and 1 Registers */
/*-----------------------*/
#define DAC0_BASE	0xFFFA8000	/* DAC 0 */

#define DAC0_CR		__REG32(DAC0_BASE + 0x000)	/* Control Register */
#define DAC0_MR		__REG32(DAC0_BASE + 0x004)	/* Mode Register */
#define DAC0_DHR	__REG32(DAC0_BASE + 0x008)	/* Data Holding Register */
#define DAC0_DOR	__REG32(DAC0_BASE + 0x00C)	/* Data Output Register */
#define DAC0_SR		__REG32(DAC0_BASE + 0x010)	/* Status Register */
#define DAC0_IER	__REG32(DAC0_BASE + 0x014)	/* Interrupt Enable Register */
#define DAC0_IDR	__REG32(DAC0_BASE + 0x018)	/* Interrupt Disable Register */
#define DAC0_IMR	__REG32(DAC0_BASE + 0x01C)	/* Interrupt Mask Register */

#define DAC1_BASE	0xFFFAC000	/* DAC 1 */

#define DAC1_CR		__REG32(DAC1_BASE + 0x000)	/* Control Register */
#define DAC1_MR		__REG32(DAC1_BASE + 0x004)	/* Mode Register */
#define DAC1_DHR	__REG32(DAC1_BASE + 0x008)	/* Data Holding Register */
#define DAC1_DOR	__REG32(DAC1_BASE + 0x00C)	/* Data Output Register */
#define DAC1_SR		__REG32(DAC1_BASE + 0x010)	/* Status Register */
#define DAC1_IER	__REG32(DAC1_BASE + 0x014)	/* Interrupt Enable Register */
#define DAC1_IDR	__REG32(DAC1_BASE + 0x018)	/* Interrupt Disable Register */
#define DAC1_IMR	__REG32(DAC1_BASE + 0x01C)	/* Interrupt Mask Register */


/* DAC_CR: Control Register */
#define DAC_SWRST		(1 << 0)	/* Reset the DAC */
#define DAC_LOOPEN		(1 << 1)	/* Automatic reaload */
#define DAC_LOOPDIS		(1 << 2)	/* WAVEND interrupt at the end of PDC */

/* DAC_MR: Mode Register */
#define DAC_TTRGEN_DIS	0x0		/* Trigger Disable */
#define DAC_TTRGEN_EN	0x1		/* Trigger Enable */
/* Trigger Selection */
#define DAC_B_TTRGSEL	1
#define DAC_TTRGSEL		(7 << DAC_B_TTRGSEL)	/* TIOA0 Trigger Selection */
#define DAC_TRG_TIOA0	(0 << DAC_B_TTRGSEL)	/* TIOA1 Trigger Selection */
#define DAC_TRG_TIOA1	(1 << DAC_B_TTRGSEL)	/* TIOA2 Trigger Selection */
#define DAC_TRG_TIOA2	(2 << DAC_B_TTRGSEL)	/* TIOA3 Trigger Selection */
#define DAC_TRG_TIOA3	(3 << DAC_B_TTRGSEL)	/* TIOA4 Trigger Selection */
#define DAC_TRG_TIOA4	(4 << DAC_B_TTRGSEL)	/* TIOA5 Trigger Selection */
#define DAC_TRG_TIOA5	(5 << DAC_B_TTRGSEL)	/* External Trigger Selection */

#define DAC_10_BIT_RES	(0 << 4)				/* 10 bits DAC Resolution */
#define DAC_8_BIT_RES	(1 << 4)				/*  8 bits DAC Resolution */

/* DAC_DHR, DAC_DOR: Data Holding and Data Output Registers */
#define DAC_DATA_10BITS	0x3FF
#define DAC_DATA_8BITS	0x0FF

/* DAC_SR,DAC_IER,DAC_IDR,DAC_IMR: Status and Interrupt Register */
#define DAC_DATRDY		(1 << 0)		/* End transfert */
#define DAC_WAVEND		(1 << 1)		/* End wave generation */
#define DAC_LOOP		(1 << 16)		/* Loop mode Status register only */



/*-----------------------*/
/* ADC 0 and 1 Registers */
/*-----------------------*/
#define ADC0_BASE	0xFFFB0000	/* Analog to Digital Converter 0 */

#define ADC0_CR		__REG32(ADC0_BASE + 0x000)	/* Control Register */
#define ADC0_MR		__REG32(ADC0_BASE + 0x004)	/* Mode Register */
#define ADC0_CHER	__REG32(ADC0_BASE + 0x010)	/* Channel Enable Register */
#define ADC0_CHDR	__REG32(ADC0_BASE + 0x014)	/* Channel Disable Register */
#define ADC0_CHSR	__REG32(ADC0_BASE + 0x018)	/* Channel Status Register */
#define ADC0_SR		__REG32(ADC0_BASE + 0x020)	/* Status Register */
#define ADC0_IER	__REG32(ADC0_BASE + 0x024)	/* Interrupt Enable Register */
#define ADC0_IDR	__REG32(ADC0_BASE + 0x028)	/* Interrupt Disable Register */
#define ADC0_IMR	__REG32(ADC0_BASE + 0x02C)	/* Interrupt Mask Register */
#define ADC0_CDR0	__REG32(ADC0_BASE + 0x030)	/* Channel Data Register */
#define ADC0_CDR1	__REG32(ADC0_BASE + 0x034)	/* Channel Data Register */
#define ADC0_CDR2	__REG32(ADC0_BASE + 0x038)	/* Channel Data Register */
#define ADC0_CDR3	__REG32(ADC0_BASE + 0x03C)	/* Channel Data Register */


#define ADC1_BASE	0xFFFB4000	/* Analog to Digital Converter 1 */

#define ADC1_CR		__REG32(ADC1_BASE + 0x000)	/* Control Register */
#define ADC1_MR		__REG32(ADC1_BASE + 0x004)	/* Mode Register */
#define ADC1_CHER	__REG32(ADC1_BASE + 0x010)	/* Channel Enable Register */
#define ADC1_CHDR	__REG32(ADC1_BASE + 0x014)	/* Channel Disable Register */
#define ADC1_CHSR	__REG32(ADC1_BASE + 0x018)	/* Channel Status Register */
#define ADC1_SR		__REG32(ADC1_BASE + 0x020)	/* Status Register */
#define ADC1_IER	__REG32(ADC1_BASE + 0x024)	/* Interrupt Enable Register */
#define ADC1_IDR	__REG32(ADC1_BASE + 0x028)	/* Interrupt Disable Register */
#define ADC1_IMR	__REG32(ADC1_BASE + 0x02C)	/* Interrupt Mask Register */
#define ADC1_CDR0	__REG32(ADC1_BASE + 0x030)	/* Channel Data Register */
#define ADC1_CDR1	__REG32(ADC1_BASE + 0x034)	/* Channel Data Register */
#define ADC1_CDR2	__REG32(ADC1_BASE + 0x038)	/* Channel Data Register */
#define ADC1_CDR3	__REG32(ADC1_BASE + 0x03C)	/* Channel Data Register */

/* ADC_CR: Control Register Bits Definition */
#define ADC_SWRST		(1 << 0)		/* ADC Software Reset */
#define ADC_START		(1 << 1)		/* ADC Start */

/* ADC_MR: Mode Register Bits Definition */
#define ADC_TRGEN_DIS	(0 << 0)		/* Trigger Disable */
#define ADC_TRGEN_EN	(1 << 0)		/* Trigger Enable */

/* Trigger Selection */
#define ADC_B_TTRGSEL	1
#define ADC_TRG_TIOA0	(0x0 << 1)		/* TIOA0 Trigger Selection */
#define ADC_TRG_TIOA1	(0x1 << 1)		/* TIOA1 Trigger Selection */
#define ADC_TRG_TIOA2	(0x2 << 1)		/* TIOA2 Trigger Selection */
#define ADC_TRG_TIOA3	(0x3 << 1)		/* TIOA3 Trigger Selection */
#define ADC_TRG_TIOA4	(0x4 << 1)		/* TIOA4 Trigger Selection */
#define ADC_TRG_TIOA5	(0x5 << 1)		/* TIOA5 Trigger Selection */
#define ADC_TRG_EXT		(0x6 << 1)		/* External Trigger Selection */

#define ADC_10_BIT_RES	(0 << 4)		/* 10 bits ADC Resolution */
#define ADC_8_BIT_RES	(1 << 4)		/*  8 bits ADC Resolution */

#define ADC_NORMAL_MODE	(0 << 5)		/* ADC Normal Mode */
#define ADC_SLEEP_MODE	(1 << 5)		/* ADC Sleep Mode */

#define ADC_PRESCAL		(0x3F << 8)	/* Max Prescaler value */
#define ADC_B_PRESCAL	8			/* bit shift */

/* ADC_CHER,ADC_CHDR,ADC_CHSR: Channel Enable, Disable and Status Registers */
#define ADC_CH0			(1 << 0)		/* Channel 0 */
#define ADC_CH1			(1 << 1)		/* Channel 1 */
#define ADC_CH2			(1 << 2)		/* Channel 2 */
#define ADC_CH3			(1 << 3)		/* Channel 3 */

/* ADC_SR,ADC_IER,ADC_IDR,ADC_IMR: Status, Enable, Disable, Mask Registers */
#define ADC_EOC0		(1 <<  0)		/* End of Conversion Channel 0 */
#define ADC_EOC1		(1 <<  1)		/* End of Conversion Channel 1 */
#define ADC_EOC2		(1 <<  2)		/* End of Conversion Channel 2 */
#define ADC_EOC3		(1 <<  3)		/* End of Conversion Channel 3 */

#define ADC_OVRE0		(1 <<  8)		/* Overrun Interrupt Error Channel 0 */
#define ADC_OVRE1		(1 <<  9)		/* Overrun Interrupt Error Channel 1 */
#define ADC_OVRE2		(1 << 10)		/* Overrun Interrupt Error Channel 2 */
#define ADC_OVRE3		(1 << 11)		/* Overrun Interrupt Error Channel 3 */

/* ADC_CDR:Convert Data Register */
#define ADC_DATA_10BITS	0x3FF
#define ADC_DATA_8BITS	0x0FF


/*---------------------------------------*/
/* Serial Peripheral Interface Registers */
/*---------------------------------------*/
#define SPI_BASE	0xFFFBC000	/* SPI */

#define SPI_CR		__REG32(SPI_BASE + 0x000)	/* Control Register */
#define SPI_MR		__REG32(SPI_BASE + 0x004)	/* Mode Register */
#define SPI_RDR		__REG32(SPI_BASE + 0x008)	/* Receive Data Register */
#define SPI_TDR		__REG32(SPI_BASE + 0x00C)	/* Transmit Data Register */
#define SPI_SR		__REG32(SPI_BASE + 0x010)	/* Status Register */
#define SPI_IER		__REG32(SPI_BASE + 0x014)	/* Interrupt Enable Register */
#define SPI_IDR		__REG32(SPI_BASE + 0x018)	/* Interrupt Disable Register */
#define SPI_IMR		__REG32(SPI_BASE + 0x01C)	/* Interrupt Mask Register */
#define SPI_RPR		__REG32(SPI_BASE + 0x020)	/* Receive Pointer Register */
#define SPI_RCR		__REG32(SPI_BASE + 0x024)	/* Receive Counter Register */
#define SPI_TPR		__REG32(SPI_BASE + 0x028)	/* Transmit Pointer Register */
#define SPI_TCR		__REG32(SPI_BASE + 0x02C)	/* Transmit Counter Register */
#define SPI_CSR0	__REG32(SPI_BASE + 0x030)	/* Chip Select Register 0 */
#define SPI_CSR1	__REG32(SPI_BASE + 0x034)	/* Chip Select Register 1 */
#define SPI_CSR2	__REG32(SPI_BASE + 0x038)	/* Chip Select Register 2 */
#define SPI_CSR3	__REG32(SPI_BASE + 0x03C)	/* Chip Select Register 3 */


/*-----------*/
/* Watchdog  */
/*-----------*/
#define WD_BASE		0xFFFF8000	/* WatchDog */

#define WD_OMR		__REG32(WD_BASE + 0x000)	/* Overflow Mode Register */
#define WD_CMR		__REG32(WD_BASE + 0x004)	/* Clock Mode Register */
#define WD_CR		__REG32(WD_BASE + 0x008)	/* Control Register */


#endif /* __AT91M55800_H__ */
