/// Black and white effect
compositor B&W
{
    technique
    {
        // Temporary textures
        texture scene target_width target_height PF_A8R8G8B8
    
        target scene
        {
            // Render output from previous compositor (or original scene)
            input previous
        }

        target_output
        {
            // Start with clear output
            input none
            // Draw a fullscreen quad with the black and white image
            pass render_quad
            {
                // Renders a fullscreen quad with a material
                material Ogre/Compositor/BlackAndWhite
                input 0 scene
            }
        }
    }
}