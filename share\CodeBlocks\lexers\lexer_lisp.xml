<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
		<Lexer name="Lisp"
				index="21"
				filemasks="*.lsp">
				<Style name="Default"
						index="0"
						fg="0,0,0"
						bg="255,255,255"
						bold="0"
						italics="0"
						underlined="0"/>
                <Style name = "Comment"
                        index="1,12"
                        fg="0,128,0"/>
                <Style name="Number"
						index="2"
						fg="240,0,240"/>
                <Style name="Keyword"
						index="3,4"
						fg="0,0,160"
						bold="1"/>
				<Style name="Symbol"
						index="5"
						fg="0,160,0"
						bold="1"/>
				<Style name="String"
						index="6,8"
						fg="0,0,255"/>
                <Style name ="Identifier"
                        index = "9"
                        fg = "0,0,0"/>
                <Style name = "Operator"
                        index="10"
                        fg="255,0,0"
                        bold="1"/>
                <Style name = "Special"
                        index = "11"
                        fg="128,0,0"/>
				<Style name="Breakpoint line"
						index="-2"
						bg="255,160,160"/>
				<Style name="Debugger active line"
						index="-3"
						bg="160,160,255"/>
				<Style name="Compiler error line"
						index="-4"
						bg="255,128,0"/>
				<Keywords>
                    <!-- Set index="0": Functions and special operators-->
                    <!-- Set index="1": "Keywords"-->
                        <Set index="1"
                        value = "not defun + - * / = &lt; &gt; &lt;= &gt;= princ eval apply funcall quote identity function complement backquote lambda set setq setf defun defmacro gensym make symbol intern symbol name symbol value symbol plist get getf putprop remprop hash make array aref car cdr caar cadr cdar cddr caaar caadr cadar caddr cdaar cdadr cddar cdddr caaaar caaadr caadar caaddr cadaar cadadr caddar cadddr cdaaar cdaadr cdadar cdaddr cddaar cddadr cdddar cddddr cons list append reverse last nth nthcdr member assoc subst sublis nsubst  nsublis remove length list length mapc mapcar mapl maplist mapcan mapcon rplaca rplacd nconc delete atom symbolp numberp boundp null listp consp minusp zerop plusp evenp oddp eq eql equal cond case and or let l if prog prog1 prog2 progn go return do dolist dotimes catch throw error cerror break continue errset baktrace evalhook truncate float rem min max abs sin cos tan expt exp sqrt random logand logior logxor lognot bignums logeqv lognand lognor logorc2 logtest logbitp logcount integer length nil"/>
				</Keywords>
				<SampleCode value="lexer_lisp.sample"
						breakpoint_line="3"
						debug_line="6"
						error_line="7"/>
                <LanguageAttributes
                    LineComment=";"
                    StreamCommentStart="#|"
                    StreamCommentEnd="|#"
                    BoxCommentStart="#| "
                    BoxCommentMid=" | "
                    BoxCommentEnd=" |#"
                    CaseSensitive="0"/>
		</Lexer>
</CodeBlocks_lexer_properties>
