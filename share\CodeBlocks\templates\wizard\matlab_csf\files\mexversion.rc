//  Mex versioning resource file
//  this should be compiled with the V4_COMPAT and ARRAY_ACCESS_INLINING
//  defines if they apply.
//
//  DO NOT EDIT    - this is for MATLAB use only

/////////////////////////////////////////////////////////////////////////////
// Define strings,
// ML_VERSION is the version of Matlab that the mex file is built against.
// INLINING determines in inlining was switched on or not.

#define ML_VERSION   100
#define INLINING     101 

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
#ifdef V4_COMPAT
    ML_VERSION,      "MATLAB 4 compatible"
#else
    ML_VERSION,      "MATLAB R11 native"
#endif
#ifdef ARRAY_ACCESS_INLINING
    INLINING,        "inlined"
#else
    INLINING,        "not inlined"
#endif
END
