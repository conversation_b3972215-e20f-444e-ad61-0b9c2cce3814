# This is a comment
## This is a comment block

import sys, time, string

month_names = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',      # These are the
               'April',   '<PERSON>',      '<PERSON><PERSON>',       # Dutch names
               "<PERSON><PERSON>",    "<PERSON>", "September",  # for the months
               "<PERSON><PERSON><PERSON>", "November", "December"]   # of the year

if len(sys.argv)!=2:
    print '''Usage: This is a 'Code::Blocks' Example'''
    print "This String goes to EOL
    var = 5
    print (f'Single quote f-string {var}')
    print (f"Double quote f-string {var}")
    print (f'''Triple single quote f-string {var}''')
    print (f"""Triple double quote f-string {var}""")
    sys.exit(0)

class ClassName:
  def perm(l):
  # Compute the list of all permutations of l
    if len(l) <= 1:
      return [l]
    r = []
    for i in range(len(l)):
      s = l[:i] + l[i+1:]
      p = perm(s)
      for x in p:
        r.append(l[i:i+1] + x)
    return r

@classmethod           # This is a decorator
@synchronized(lock)    # And so is this
def func(self):
  try:
    return  """A "Triple Double Quote" String\n"""
  except SystemExit:
    pass
