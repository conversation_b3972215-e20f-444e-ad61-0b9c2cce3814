/*
 * Sample preview code
 * This is a block comment
 */

import java.io.*; // this is a line comment

/**
  * This is a documentation comment block
  * @param xxx does this (this is the documentation keyword)
  * @authr some user (this is the documentation keyword *error*)
  */

public class A
{
	public static void main(String args[])
	{
		int numbers[20];
		int average = 0;
		char ch = '\n';
		for (int i = 0; i < 20; ++i) 	// a breakpoint is set
		{
			numbers[i] = i; // active line (during debugging)
			total += i; // error line
		}
		average = total / 20;
		System.out.print("total:");
		System.out.print(total);
		System.out.print("average:");
		System.out.println(average);
	}
}