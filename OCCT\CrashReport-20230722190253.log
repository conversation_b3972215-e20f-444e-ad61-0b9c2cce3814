{"IsDevelopment": false, "Edition": "Coolermaster", "Version": "12.0.9", "Message": "Failed to start a process with file path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\OCCT\\CPUOCCT\\CpuOcct64.exe'. Target file or working directory doesn't exist, or the provided credentials are invalid.", "Exception": "System.ComponentModel.Win32Exception (0x80004005): Failed to start a process with file path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\OCCT\\CPUOCCT\\CpuOcct64.exe'. Target file or working directory doesn't exist, or the provided credentials are invalid.\r\n ---> System.ComponentModel.Win32Exception (225): An error occurred trying to start process 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\OCCT\\CPUOCCT\\CpuOcct64.exe' with working directory 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\OCCT\\CPUOCCT'. Operation did not complete successfully because the file contains a virus or potentially unwanted software.\r\n   at System.Diagnostics.Process.StartWithCreateProcess(ProcessStartInfo startInfo)\r\n   at System.Diagnostics.Process.Start()\r\n   at CliWrap.Utils.ProcessEx.Start() in D:\\a\\CliWrap\\CliWrap\\CliWrap\\Utils\\ProcessEx.cs:line 61\r\n   at CliWrap.Utils.ProcessEx.Start() in D:\\a\\CliWrap\\CliWrap\\CliWrap\\Utils\\ProcessEx.cs:line 73\r\n   at CliWrap.Command.ExecuteAsync(CancellationToken forcefulCancellationToken, CancellationToken gracefulCancellationToken) in D:\\a\\CliWrap\\CliWrap\\CliWrap\\Command.Execution.cs:line 275\r\n   at CliWrap.EventStream.EventStreamCommandExtensions.<>c__DisplayClass4_0.<Observe>b__0(IObserver`1 observer) in D:\\a\\CliWrap\\CliWrap\\CliWrap\\EventStream\\PushEventStreamCommandExtensions.cs:line 48\r\n   at CliWrap.Utils.<Observable>F9A56BC066C1C55331A426EEC55B608B3C789C113870FC381D9D496607E7DCF71__Observable`1.Subscribe(IObserver`1 observer) in D:\\a\\CliWrap\\CliWrap\\CliWrap\\Utils\\Observable.cs:line 48\r\n   at OcctCore.Models.Tests.TestProcess..ctor(String filename, List`1 arguments, Dictionary`2 envVariables)\r\n   at OcctCore.Models.Tests.TestWorkerShMemBase`2.StartAsync(TestConfig config, TestResult testResult, Boolean isStabilityCertificate)\r\n   at EHOKHKOCCPKHNLJFCMKDNMMJKFELPJEBGEAL.HGLOCOOAGAEBKGHJDFCNEEIMNJDNMAGBEOCO._StartAsync(TestConfig  , Boolean  )\r\n   at OcctCore.Models.Tests.TestBase.StartAsync(TestConfig config, Boolean isStabilityCertificate, Stopwatch mainStopwatch, SourceList`1 log, Boolean isInfinite, TimeSpan duration, Boolean ignoreStartStopLogLine)\r\n   at OcctCore.Models.Tests.PowerSupply.PowerSupply._StartAsync(TestConfig config, Boolean isStabilityCertificate)\r\n   at OcctCore.Models.Tests.TestBase.StartAsync(TestConfig config, Boolean isStabilityCertificate, Stopwatch mainStopwatch, SourceList`1 log, Boolean isInfinite, TimeSpan duration, Boolean ignoreStartStopLogLine)\r\n   at OcctCore.Models.Tests.SchedulePeriodMachine.NMLKCCCDNCPGOLIMDBALANHONMBLODJAFDBL()\r\n   at Stateless.StateMachine`2.StateRepresentation.ExecuteEntryActionsAsync(Transition transition, Object[] entryArgs)\r\n   at Stateless.StateMachine`2.StateRepresentation.EnterAsync(Transition transition, Object[] entryArgs)\r\n   at Stateless.StateMachine`2.EnterStateAsync(StateRepresentation representation, Transition transition, Object[] args)\r\n   at Stateless.StateMachine`2.HandleTransitioningTriggerAsync(Object[] args, StateRepresentation representativeState, Transition transition)\r\n   at Stateless.StateMachine`2.InternalFireOneAsync(TTrigger trigger, Object[] args)\r\n   at Stateless.StateMachine`2.InternalFireQueuedAsync(TTrigger trigger, Object[] args)\r\n   at Stateless.StateMachine`2.InternalFireAsync(TTrigger trigger, Object[] args)\r\n   at LCDBDLFEDOPAGFKJMNKLEBINJKPHDIGJPFEA.OCHLGMNAHFPMFFMCCEGDNFOLGPFBABHFGOFM(TaskAwaiter& , LCDBDLFEDOPAGFKJMNKLEBINJKPHDIGJPFEA )\r\n   at OcctCore.Models.Tests.SchedulePeriodMachine.StartAsync(ISchedulePeriod schedulePeriod, Boolean isStabilityCertificate, Stopwatch stopwatch)\r\n   at OcctCore.Models.Tests.ScheduleMachine.KKMHPLNIFPJKGBAEHJCOPBFGHNHFCLCMFDAD()\r\n   at Stateless.StateMachine`2.StateRepresentation.ExecuteEntryActionsAsync(Transition transition, Object[] entryArgs)\r\n   at Stateless.StateMachine`2.StateRepresentation.EnterAsync(Transition transition, Object[] entryArgs)\r\n   at Stateless.StateMachine`2.EnterStateAsync(StateRepresentation representation, Transition transition, Object[] args)\r\n   at Stateless.StateMachine`2.HandleTransitioningTriggerAsync(Object[] args, StateRepresentation representativeState, Transition transition)\r\n   at Stateless.StateMachine`2.InternalFireOneAsync(TTrigger trigger, Object[] args)\r\n   at Stateless.StateMachine`2.InternalFireQueuedAsync(TTrigger trigger, Object[] args)\r\n   at Stateless.StateMachine`2.InternalFireAsync(TTrigger trigger, Object[] args)\r\n   at LCDBDLFEDOPAGFKJMNKLEBINJKPHDIGJPFEA.OCHLGMNAHFPMFFMCCEGDNFOLGPFBABHFGOFM(TaskAwaiter& , LCDBDLFEDOPAGFKJMNKLEBINJKPHDIGJPFEA )\r\n   at OcctCore.Models.Tests.ScheduleMachine.StartAsync(IOcctSchedule schedule, Boolean autoStart)\r\n   at KAGOILFNBBPOMDOKHAEAEDKOHIELJFFEMLFA.OCHLGMNAHFPMFFMCCEGDNFOLGPFBABHFGOFM(TaskAwaiter& , KAGOILFNBBPOMDOKHAEAEDKOHIELJFFEMLFA )\r\n   at OcctGuiAvalonia.ViewModels.Tabs.MainTabsViewModel.<>c__DisplayClass81_0.NKOCKFEJGOKKMKJCKEDODMCOHHHNJPIGOHIP.MoveNext()"}