<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
		<Lexer name="Squirrel"
				index="3"
				filemasks="*.nut,*.script">
				<Style name="Default"
						index="0"
						fg="0,0,0"
						bg="255,255,255"
						bold="0"
						italics="0"
						underlined="0"/>
				<Style name="Comment (normal)"
						index="1,2"
						fg="160,160,160"/>
				<Style name="Comment (documentation)"
						index="3,15"
						fg="128,128,255"
						bold="1"/>
				<Style name="Comment keyword (documentation)"
						index="17"
						fg="0,128,128"/>
				<Style name="Comment keyword error (documentation)"
						index="18"
						fg="128,0,0"/>
				<Style name="Number"
						index="4"
						fg="240,0,240"/>
				<Style name="Keyword"
						index="5"
						fg="0,0,160"
						bold="1"/>
				<Style name="User keyword"
						index="16"
						fg="0,160,0"
						bold="1"/>
				<Style name="String"
						index="6,12"
						fg="0,0,255"/>
				<Style name="Verbatim String"
						index="13"
						fg="0,0,255"/>
				<Style name="Character"
						index="7"
						fg="224,160,0"/>
				<Style name="UUID"
						index="8"
						fg="0,0,0"/>
				<Style name="Preprocessor"
						index="9"
						fg="0,160,0"/>
				<Style name="Operator"
						index="10"
						fg="255,0,0"/>
				<Keywords>
						<Language index="0"
								value="break case catch class clone continue
										default delegate delete else enum extends for
										foreach function if in local null resume
										return switch this throw try typeof
										while parent yield constructor vargc vargv
										instanceof true false static"/>
						<Documentation index="2"
								value="a addindex addtogroup anchor arg attention
								author b brief bug c class code date def defgroup deprecated dontinclude
								e em endcode endhtmlonly endif endlatexonly endlink endverbatim enum example exception
								f$ f[ f] file fn hideinitializer htmlinclude htmlonly
								if image include ingroup internal invariant interface latexonly li line link
								mainpage name namespace nosubgrouping note overload
								p page par param post pre ref relates remarks return retval
								sa section see showinitializer since skip skipline struct subsection
								test throw todo typedef union until
								var verbatim verbinclude version warning weakgroup $ @ \ &amp; &lt; &gt; # { }"/>
				</Keywords>
				<SampleCode value="lexer_squirrel.sample"/>
				<LanguageAttributes
                    LineComment="//"
                    StreamCommentStart="/*"
                    StreamCommentEnd="*/"
                    BoxCommentStart="/* "
                    BoxCommentMid=" * "
                    BoxCommentEnd=" */"
                    LexerCommentStyles="1,2,3,15,17,18,23,65,66,67,79,81,82,87"
                    LexerCharacterStyles="7,71"
                    LexerStringStyles="6,12,70,76"
                    LexerPreprocessorStyles="9,73"/>
		</Lexer>
</CodeBlocks_lexer_properties>
