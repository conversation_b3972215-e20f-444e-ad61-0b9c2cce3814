<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
  <Lexer name="VHDL"
         index="64"
         filemasks="*.vhd,*.vhdl">
    <!-- #define wxSCI_VHDL_DEFAULT 0 -->
    <Style name="Default"
           index="0"
           fg="0,0,0"
           bg="255,255,255"
           bold="0"
           italics="0"
           underlined="0" />
    <!--#define wxSCI_VHDL_COMMENT 1
                #define wxSCI_VHDL_COMMENTLINEBANG 2-->
    <Style name="Comment"
           index="1"
           fg="34,138,34" />
    <Style name="DocComment"
           index="2"
           fg="128,128,255" />
    <Style name="blockComment"
           index="15"
           fg="34,138,34" />
    <!--fg="160,160,160"-->
    <!--#define wxSCI_VHDL_NUMBER 3-->
    <Style name="Number"
           index="3"
           fg="160,882,45" />
    <!--fg="240,0,240"/-->
    <!--#define wxSCI_VHDL_STRING 4
                #define wxSCI_VHDL_STRINGEOL 7-->
    <Style name="String"
           index="4,7"
           fg="0,0,255" />
    <!--#define wxSCI_VHDL_OPERATOR 5
                #define wxSCI_VHDL_STDOPERATOR 9-->
    <Style name="Operator"
           index="5,9"
           fg="0,0,128"
           bold="1" />
    <!--#define wxSCI_VHDL_KEYWORD 8-->
    <Style name="Keyword"
           index="8"
           fg="0,0,160"
           bold="1" />
    <!--#define wxSCI_VHDL_USERWORD 14-->
    <Style name="User keyword"
           index="14"
           fg="0,160,0"
           bold="1" />
    <!--#define wxSCI_VHDL_ATTRIBUTE 10-->
    <Style name="Attribute"
           index="10"
           fg="95,159,159" />
    <!--#define wxSCI_VHDL_STDPACKAGE 12-->
    <Style name="Std Package"
           index="12"
           fg="255,0,255" />
    <!--#define wxSCI_VHDL_STDTYPE 13-->
    <Style name="Std Type"
           index="13"
           fg="159,159,95" />
    <!--#define wxSCI_VHDL_IDENTIFIER 6-->
    <Style name="Identifier"
           index="6"
           fg="0,0,0" />
    <!--#define wxSCI_VHDL_STDFUNCTION 11-->
    <Style name="Std Function"
           index="11"
           fg="0,0,255" />
    <Style name="Breakpoint line"
           index="-2"
           bg="255,160,160" />
    <Style name="Debugger active line"
           index="-3"
           bg="160,160,255" />
    <Style name="Compiler error line"
           index="-4"
           bg="255,128,0" />
    <Keywords>
      <!-- Primary keywords and identifiers -->
      <Set index="0"
           value="access after alias all architecture array assert attribute begin block body buffer bus case component
                                configuration constant disconnect downto else elsif end entity exit file for function generate generic
                                group guarded if impure in inertial inout is label library linkage literal loop map new next null of
                                on open others out package port postponed procedure process pure range record register reject report
                                return select severity shared signal subtype then to transport type unaffected units until use variable
                                wait when while with" />
      <Set index="1"
           value="abs and mod nand nor not or rem rol ror sla sll sra srl xnor xor" />
      <Set index="2"
           value="left right low high ascending image value pos val succ pred leftof rightof base range reverse_range
                                length delayed stable quiet transaction event active last_event last_active last_value driving
                                driving_value simple_name path_name instance_name" />
      <Set index="3"
           value="now readline read writeline write endfile resolved to_bit to_bitvector to_stdulogic to_stdlogicvector
                                to_stdulogicvector to_x01 to_x01z to_UX01 rising_edge falling_edge is_x shift_left shift_right rotate_left
                                rotate_right resize to_integer to_unsigned to_signed std_match to_01" />
      <Set index="4"
           value="std ieee work standard textio std_logic_1164 std_logic_arith std_logic_misc std_logic_signed
                                std_logic_textio std_logic_unsigned numeric_bit numeric_std math_complex math_real vital_primitives
                                vital_timing" />
      <Set index="5"
           value="boolean bit character severity_level integer real time delay_length natural positive string bit_vector
                                file_open_kind file_open_status line text side width std_ulogic std_ulogic_vector std_logic
                                std_logic_vector X01 X01Z UX01 UX01Z unsigned signed" />
    </Keywords>
    <SampleCode value="lexer_vhdl.sample"
                breakpoint_line="32"
                debug_line="35"
                error_line="37" />
    <LanguageAttributes
        LineComment="--"
        DoxygenLineComment="--!"
        StreamCommentStart=""
        StreamCommentEnd=""
        DoxygenStreamCommentStart=""
        DoxygenStreamCommentEnd=""
        BoxCommentStart="--"
        BoxCommentMid="--"
        BoxCommentEnd="--"
        CaseSensitive="0"
        LexerCommentStyles="1,2"
        LexerCharacterStyles=""
        LexerStringStyles="4,7"
        LexerPreprocessorStyles=""/>
  </Lexer>
</CodeBlocks_lexer_properties>
