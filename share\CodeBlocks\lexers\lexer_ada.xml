<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
		<Lexer name="Ada"
				index="20"
				filemasks="*.ads,*.adb">
				<!--#define wxSCI_ADA_DEFAULT 0-->
                <Style name="Default"
						index="0"
						fg="0,0,0"
						bg="255,255,255"
						bold="0"
						italics="0"
						underlined="0"/>
                <!--#define wxSCI_ADA_NUMBER 3-->
                <Style name="Number"
						index="3"
						fg="255,128,00"/>
						<!--fg="240,0,240"/-->
				<!--#define wxSCI_ADA_COMMENTLINE 10-->
				<Style name="Comment"
						index="10"
						fg="0,128,00"/>
                <!--#define wxSCI_ADA_STRING 7
                #define wxSCI_ADA_STRINGEOL 8-->
                <Style name="String"
						index="7,8"
						fg="128,128,128"/>
                <!--#define wxSCI_ADA_IDENTIFIER 2-->
                <Style name="Identifier"
                        index="2"
                        fg="0,0,0"/>
                <!--#define wxSCI_ADA_CHARACTER 5
                #define wxSCI_ADA_CHARACTEREOL 6-->
                <Style name="Character"
						index="5,6"
						fg="128,128,128"/>
                <!--#define wxSCI_ADA_WORD 1 -->
                <!--keywordClass="instre1" /> -->
                <Style name="Instruction-Word"
						index="1"
						fg="0,0,255"
						bold="1"/>
                <!-- #define wxSCI_ADA_DELIMITER 4 -->
                <Style name="Delimiter"
                        index="4"
                        fg="255,128,128"
                        bold="1"/>
                <!-- #define wxSCI_ADA_LABEL 9 -->
                <Style name="Label"
                        index="9"
                        fg="128,64,0"/>
                <!-- #define wxSCI_ADA_ILLEGAL 11 -->
                <Style name="Illegal"
                        index="11"
                        fg="255,0,0"/>
				<Style name="Breakpoint line"
						index="-2"
						bg="255,160,160"/>
				<Style name="Debugger active line"
						index="-3"
						bg="160,160,255"/>
				<Style name="Compiler error line"
						index="-4"
						bg="255,128,0"/>
				<Keywords>
                        <!-- Primary keywords and identifiers -->
                        <Set index="0"
                            value="abort abstract accept access aliased all and array
                                at begin body case constant declare delay delta digits
                                do else elsif end entry exception exit for function
                                generic goto if in is limited loop new not null of
                                others out or package pragma private procedure protected
                                raise range record renames requeue return reverse select
                                separate subtype tagged task terminate then type until
                                use when while with"/>
				</Keywords>
				<SampleCode value="lexer_ada.sample"
						breakpoint_line="32"
						debug_line="35"
						error_line="37"/>
                <LanguageAttributes
                    LineComment="--"
                    StreamCommentStart=""
                    StreamCommentEnd=""
                    BoxCommentStart=""
                    BoxCommentMid=""
                    BoxCommentEnd=""
                    CaseSensitive="0"/>
		</Lexer>
</CodeBlocks_lexer_properties>
