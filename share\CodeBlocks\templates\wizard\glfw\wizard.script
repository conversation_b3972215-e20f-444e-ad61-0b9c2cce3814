////////////////////////////////////////////////////////////////////////////////
//
// GLFW project wizard
//
////////////////////////////////////////////////////////////////////////////////

// globals
GlfwPathDefault    <- _T("$(#glfw)");
GlfwPathDefaultInc <- _T("$(#glfw.include)");
GlfwPathDefaultLib <- _T("$(#glfw.lib)");
GlfwPath <- _T("");

function BeginWizard()
{
    local intro_msg = _T("Welcome to the new GLFW project wizard!\n\n" +
                         "This wizard will guide you to create a new project\n" +
                         "using the GLFW OpenGL extensions.\n\n" +
                         "When you 're ready to proceed, please click \"Next\"...");

    local glfwpath_descr = _T("Please select the location of GLFW on your computer.\n" +
                              "This is the top-level folder where GLFW was installed (unpacked).\n" +
                              "To help you, this folder must contain the subfolders\n" +
                              "\"include\" and \"lib\".");

    Wizard.AddInfoPage(_T("GlfwIntro"), intro_msg);
    Wizard.AddProjectPathPage();
    Wizard.AddGenericSelectPathPage(_T("GlfwPath"), glfwpath_descr, _T("Please select GLFW's location:"), GlfwPathDefault);
    Wizard.AddCompilerPage(_T(""), _T("*"), true, true);
}

////////////////////////////////////////////////////////////////////////////////
// GLFW's path page
////////////////////////////////////////////////////////////////////////////////

function OnLeave_GlfwPath(fwd)
{
    if (fwd)
    {
        local dir         = Wizard.GetTextControlValue(_T("txtFolder")); // txtFolder is the text control in GenericSelectPathPage
        local dir_nomacro = VerifyDirectory(dir);

        if (dir_nomacro.IsEmpty())
            return false;

        // verify include dependencies
        local dir_nomacro_inc = GetCompilerIncludeDir(dir, GlfwPathDefault, GlfwPathDefaultInc);
        if (dir_nomacro_inc.IsEmpty())
            return false;
        if (!VerifyFile(dir_nomacro_inc + wxFILE_SEP_PATH + _T("GLFW"), _T("glfw3.h"), _T("GLFW's include"))) return false;

        // verify library dependencies
        local dir_nomacro_lib = GetCompilerLibDir(dir, GlfwPathDefault, GlfwPathDefaultLib);
        if (dir_nomacro_lib.IsEmpty())
            return false;
        if (!VerifyLibFile(dir_nomacro_lib, _T("glfw3"), _T("GLFW's"))) return false;


        GlfwPath = dir; // Remember the original selection.

        local is_macro = _T("");

        // try to resolve the include directory as macro
        is_macro = GetCompilerIncludeMacro(dir, GlfwPathDefault, GlfwPathDefaultInc);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real inc path we had computed instead
            GlfwPathDefaultInc = dir_nomacro_inc;
        }

        // try to resolve the library directory as macro
        is_macro = GetCompilerLibMacro(dir, GlfwPathDefault, GlfwPathDefaultLib);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real lib path we had computed instead
            GlfwPathDefaultLib = dir_nomacro_lib;
        }
    }
    return true;
}

// return the files this project contains
function GetFilesDir()
{
    return _T("glfw/files");
}

// setup the already created project
function SetupProject(project)
{
    // set project options
    project.AddIncludeDir(GlfwPathDefaultInc);
    project.AddLibDir(GlfwPathDefaultLib);

    // add link libraries
    project.AddLinkLib(_T("glfw3"));
    if (PLATFORM == PLATFORM_MSW)
    {
        project.AddLinkLib(_T("opengl32"));
        project.AddLinkLib(_T("glu32"));
        project.AddLinkLib(_T("gdi32"));
    }
    else
    {
        project.AddLinkLib(_T("GL"));
        project.AddLinkLib(_T("GLU"));
        project.AddLinkLib(_T("pthread"));
        project.AddLinkLib(_T("Xxf86vm"));
    }

    // enable compiler warnings (project-wide)
    WarningsOn(project, Wizard.GetCompilerID());

    // Debug
    local target = project.GetBuildTarget(Wizard.GetDebugName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttConsoleOnly); // ttConsoleOnly: console for debugging
        target.SetOutputFilename(Wizard.GetDebugOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        target.SetWorkingDir(GlfwPath + _T("/bin"));
        // enable generation of debugging symbols for target
        DebugSymbolsOn(target, Wizard.GetCompilerID());
    }

    // Release
    target = project.GetBuildTarget(Wizard.GetReleaseName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttExecutable); // ttExecutable: no console
        target.SetOutputFilename(Wizard.GetReleaseOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        target.SetWorkingDir(GlfwPath + _T("/bin"));
        // enable optimizations for target
        OptimizationsOn(target, Wizard.GetCompilerID());
    }

    return true;
}
