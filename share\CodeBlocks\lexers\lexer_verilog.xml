<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
  <Lexer name="Verilog"
         index="56"
         filemasks="*.v,*.verilog">
    <!-- #define wxSCI_V_DEFAULT 0 -->
    <Style name="Default"
           index="0"
           fg="0,0,0"
           bg="255,255,255"
           bold="0"
           italics="0"
           underlined="0" />
    <!--#define wxSCI_V_COMMENT 1
                #define wxSCI_V_COMMENTLINE 2
                #define wxSCI_V_COMMENTLINEBANG 3-->
    <Style name="Comment"
           index="1,2,3"
           fg="34,138,34" />
    <!--#define wxSCI_V_NUMBER 4-->
    <Style name="Number"
           index="4"
           fg="160,882,45" />
    <!--fg="240,0,240"/-->
    <!--#define wxSCI_V_STRING 6
                #define wxSCI_V_STRINGEOL 12-->
    <Style name="String"
           index="6,12"
           fg="0,0,255" />
    <!--#define wxSCI_V_OPERATOR 10-->
    <Style name="Operator"
           index="10"
           fg="0,0,128"
           bold="1" />
    <!-- #define wxSCI_V_PREPROCESSOR 9 -->
    <Style name="Preprocessor"
           index="9"
           fg="128,64,0" />
    <!--#define wxSCI_V_IDENTIFIER 11-->
    <Style name="Identifier"
           index="11"
           fg="0,0,0" />
    <!-- #define wxSCI_V_WORD 5 -->
    <Style name="Instructions"
           index="5"
           fg="0,0,255"
           bold="1" />
    <!-- #define wxSCI_V_WORD2 7 -->
    <Style name="Keywords"
           index="7"
           fg="128,0,255" />
    <!-- #define wxSCI_V_WORD3 8 -->
    <Style name="Tagname"
           index="8"
           fg="0,0,0" />
    <!-- #define wxSCI_V_USER 19 -->
    <Style name="User"
           index="19"
           fg="0,0,0" />
    <Style name="Breakpoint line"
           index="-2"
           bg="255,160,160" />
    <Style name="Debugger active line"
           index="-3"
           bg="160,160,255" />
    <Style name="Compiler error line"
           index="-4"
           bg="255,128,0" />
    <Keywords>
      <!-- Primary keywords and identifiers -->
      <Set index="0"
           value="always and assign begin xbuf buf bufif0 bufif1 case casex casez cmos
                                default defparam else end endcase endfunction endmodule endprimitive endspecify
                                endtable endtask event for force forever fork function if initial inout input
                                integer join macromodule makefile module nand negedge nmos nor not notif0 notif1
                                or output parameter pmos posedge primitive pulldown pullup rcmos real realtime reg
                                repeat rnmos rpmos rtran rtranif0 rtranif1 signed specify specparam supply supply0 supply1 table
                                task time tran tranif0 tranif1 tri tri0 tri1 triand trior trireg vectored wait
                                wand while wire wor xnor xor" />
      <!-- Secondary keywords and identifiers -->
      <Set index="1"
           value="" />
      <!-- System Tasks -->
      <Set index="2"
           value="$readmemb $readmemh $sreadmemb $sreadmemh $display $write $strobe $monitor $fdisplay $fwrite $fstrobe
                                $fmonitor $fopen $fclose $time $stime $realtime $scale $printtimescale $timeformat $stop $finish $save
                                $incsave $restart $input $log $nolog $key $nokey $scope $showscopes $showscopes $showvars $showvars
                                $countdrivers $list $monitoron $monitoroff $dumpon $dumpoff $dumpfile $dumplimit $dumpflush $dumpvars
                                $dumpall $reset $reset $reset $reset $reset $random $getpattern $rtoi $itor $realtobits $bitstoreal
                                $setup $hold $setuphold $period $width $skew $recovery" />
      <!-- User defined tasks and identifiers -->
      <Set index="3"
           value="" />
    </Keywords>
    <SampleCode value="lexer_verilog.sample"
                breakpoint_line="8"
                debug_line="10"
                error_line="11" />
    <LanguageAttributes
        LineComment="//"
        StreamCommentStart="/*"
        StreamCommentEnd="*/"
        BoxCommentStart="/* "
        BoxCommentMid=" * "
        BoxCommentEnd=" */"
        CaseSensitive="1"
        LexerCommentStyles="1,2,3"
        LexerCharacterStyles=""
        LexerStringStyles="6,12"
        LexerPreprocessorStyles="9"/>
  </Lexer>
</CodeBlocks_lexer_properties>
