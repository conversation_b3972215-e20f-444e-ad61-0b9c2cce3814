#	Blender TexTools, 
#	<TexTools, Blender addon for editing UVs and Texture maps.>
#	Copyright (C) <2018> <renderhjs>

#	This program is free software: you can redistribute it and/or modify
#	it under the terms of the GNU General Public License as published by
#	the Free Software Foundation, either version 3 of the License, or
#	(at your option) any later version.
#
#	This program is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
#	GNU General Public License for more details.
#
#	You should have received a copy of the GNU General Public License
#	along with this program. If not, see <http://www.gnu.org/licenses/>.


#	Credits
#
#	UVSquares: 
#	<Uv Squares, Blender addon for reshaping UV vertices to grid.>
#   Copyright (C) <2014> <Re<PERSON>>
#	https://github.com/JoseConseco/UvSquares/blob/master/uv_squares.py
#   
#   Current maintainers: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.
#
#   Icons art. <PERSON> (activemotionpictures)
