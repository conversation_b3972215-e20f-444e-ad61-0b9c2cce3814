////////////////////////////////////////////////////////////////////////////////
//
// Qt5 project wizard
//
////////////////////////////////////////////////////////////////////////////////

// globals
QtPathDefault    <- _T("$(#qt5)");
QtPathDefaultInc <- _T("$(#qt5.include)");
QtPathDefaultLib <- _T("$(#qt5.lib)");
QtPath <- _T("");

function BeginWizard()
{
    local intro_msg = _T("Welcome to the new Trolltech Qt5 project wizard!\n" +
                         "This wizard will guide you to create a new Qt5 project\n" +
                         "using the Trolltech Qt5 cross-platform GUI toolkit\n\n" +
                         "When you're ready to proceed, please click \"Next\"...");

    local qtpath_msg = _T("Please select the location of Trolltech Qt5 on your computer.\n" +
                          "This is the top-level folder where Qt5 was installed.\n" +
                          "To help you, this folder must contain the subfolders\n" +
                          "\"include\" and \"lib\".");

    Wizard.AddInfoPage(_T("QtIntro"), intro_msg);
    Wizard.AddProjectPathPage();
    Wizard.AddGenericSelectPathPage(_T("QtPath"), qtpath_msg, _T("Qt's location:"), QtPathDefault);
    Wizard.AddCompilerPage(_T(""), _T("gcc*"), true, true);
}

////////////////////////////////////////////////////////////////////////////////
// Qt's path page
////////////////////////////////////////////////////////////////////////////////

function OnLeave_QtPath(fwd)
{
    if (fwd)
    {
        local dir         = Wizard.GetTextControlValue(_T("txtFolder")); // txtFolder is the text control in GenericSelectPathPage
        local dir_nomacro = VerifyDirectory(dir);

        if (dir_nomacro.IsEmpty())
            return false;

        // verify include dependencies
        local dir_nomacro_inc = GetCompilerIncludeDir(dir, QtPathDefault, QtPathDefaultInc);
        if (dir_nomacro_inc.IsEmpty())
            return false;
        if (PLATFORM == PLATFORM_MSW
                && !VerifyFile(dir_nomacro_inc + wxFILE_SEP_PATH + _T("QtWidgets"), _T("QApplication"), _T("Qt's include")))
            return false;

        // verify library dependencies
        local dir_nomacro_lib = GetCompilerLibDir(dir, QtPathDefault, QtPathDefaultLib);
        if (dir_nomacro_lib.IsEmpty())
            return false;
        if (PLATFORM == PLATFORM_MSW && !VerifyLibFile(dir_nomacro_lib, _T("libQt5Core"), _T("Qt's")))
            return false;
        else if (PLATFORM != PLATFORM_MSW && !VerifyLibFile(dir_nomacro_lib, _T("Qt5Core"), _T("Qt's")))
            return false;


        QtPath = dir; // Remember the original selection.

        local is_macro = _T("");

        // try to resolve the include directory as macro
        is_macro = GetCompilerIncludeMacro(dir, QtPathDefault, QtPathDefaultInc);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real inc path we had computed instead
            QtPathDefaultInc = dir_nomacro_inc;
        }

        // try to resolve the library directory as macro
        is_macro = GetCompilerLibMacro(dir, QtPathDefault, QtPathDefaultLib);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real lib path we had computed instead
            QtPathDefaultLib = dir_nomacro_lib;
        }
    }
    return true;
}

// return the files this project contains
function GetFilesDir()
{
    return _T("qt5/files");
}

// setup the already created project
function SetupProject(project)
{
    project.AddIncludeDir(QtPathDefaultInc);
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("ActiveQt"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtCLucene"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtConcurrent"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtCore"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtDeclarative"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtDesigner"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtDesignerComponents"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtGui"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtHelp"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtMultimedia"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtMultimediaQuick_p"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtMultimediaWidgets"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtNetwork"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtOpenGL"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtOpenGLExtensions"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtPlatformSupport"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtPrintSupport"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtQml"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtQuick"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtQuickParticles"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtQuickTest"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtScript"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtScriptTools"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtSensors"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtSerialPort"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtSql"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtSvg"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtTest"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtUiTools"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtV8"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtWebKit"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtWebKitWidgets"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtWidgets"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtXml"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtXmlPatterns"));
    project.AddIncludeDir(QtPathDefaultInc + wxFILE_SEP_PATH + _T("QtZlib"));

    project.AddLibDir(QtPathDefaultLib);

    // add link libraries
    if (PLATFORM == PLATFORM_MSW)
    {
        project.AddLinkLib(_T("libQt5Core"));
        project.AddLinkLib(_T("libQt5Gui"));
        project.AddLinkLib(_T("libQt5Widgets"));
    }
    else
    {
        project.AddLinkLib(_T("Qt5Core"));
        project.AddLinkLib(_T("Qt5Gui"));
        project.AddLinkLib(_T("Qt5Widgets"));
    }

    // enable compiler warnings (project-wide)
    WarningsOn(project, Wizard.GetCompilerID());

    // Debug
    local target = project.GetBuildTarget(Wizard.GetDebugName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttConsoleOnly); // ttConsoleOnly: console for debugging
        target.SetOutputFilename(Wizard.GetDebugOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        // enable generation of debugging symbols for target
        DebugSymbolsOn(target, Wizard.GetCompilerID());
    }

    // Release
    target = project.GetBuildTarget(Wizard.GetReleaseName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttExecutable); // ttExecutable: no console
        target.SetOutputFilename(Wizard.GetReleaseOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        // enable optimizations for target
        OptimizationsOn(target, Wizard.GetCompilerID());
    }

    return true;
}
