////////////////////////////////////////////////////////////////////////////////
//
// SDL project wizard
//
////////////////////////////////////////////////////////////////////////////////

// globals (used under windows only)
SDLPathDefault    <- _T("$(#sdl)");
SDLPathDefaultInc <- _T("$(#sdl.include)");
SDLPathDefaultLib <- _T("$(#sdl.lib)");
SDLPath           <- _T("");

function BeginWizard()
{
    local intro_msg = _T("Welcome to the new SDL project wizard!\n\n" +
                         "This wizard will guide you to create a new project\n" +
                         "using the SDL graphics library.\n\n" +
                         "When you 're ready to proceed, please click \"Next\"...");

    Wizard.AddInfoPage(_T("SDLIntro"), intro_msg);
    Wizard.AddProjectPathPage();
    if (PLATFORM == PLATFORM_MSW)
    {
        local sdlpath_descr = _T("Please select the location of SDL on your computer.\n" +
                                 "This is the top-level folder where SDL was installed (unpacked).\n" +
                                 "To help you, this folder must contain the subfolders\n" +
                                 "\"include\" and \"lib\".\n\n" +
                                 "You can also use a global variable, p.e. $(#sdl)\n");

        Wizard.AddGenericSelectPathPage(_T("SDLPath"), sdlpath_descr, _T("Please select SDL's location:"), SDLPathDefault);
    }

    Wizard.AddCompilerPage(_T(""), _T("*"), true, true);
}

////////////////////////////////////////////////////////////////////////////////
// SDL's path page
////////////////////////////////////////////////////////////////////////////////

function OnLeave_SDLPath(fwd)
{
    if (fwd)
    {
        local dir         = Wizard.GetTextControlValue(_T("txtFolder")); // txtFolder is the text control in GenericSelectPathPage
        local dir_nomacro = VerifyDirectory(dir);

        if (dir_nomacro.IsEmpty())
            return false;

        // verify include dependencies
        local dir_nomacro_inc = GetCompilerIncludeDir(dir, SDLPathDefault, SDLPathDefaultInc);
        if (dir_nomacro_inc.IsEmpty())
            return false;

        if (!IO.FileExists(dir_nomacro_inc + wxFILE_SEP_PATH + _T("SDL.h")))
        {
            if (VerifyFile(dir_nomacro_inc, _T("SDL/SDL.h"), _T("SDL's include")))
                SDLPathDefaultInc = SDLPathDefaultInc + _T("/SDL");
            else
                return false;
        }

        // verify library dependencies
        local dir_nomacro_lib = GetCompilerLibDir(dir, SDLPathDefault, SDLPathDefaultLib);
        if (dir_nomacro_lib.IsEmpty())
            return false;

        if (!VerifyLibFile(dir_nomacro_lib, _T("SDLmain"), _T("SDL's")))
            return false;

        if (!VerifyLibFile(dir_nomacro_lib, _T("SDL.dll"), _T("SDL's")))
            return false;

        SDLPath = dir; // Remember the original selection.

        local is_macro = _T("");

        // try to resolve the include directory as macro
        is_macro = GetCompilerIncludeMacro(dir, SDLPathDefault, SDLPathDefaultInc);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real inc path we had computed instead
            SDLPathDefaultInc = dir_nomacro_inc;
        }

        // try to resolve the library directory as macro
        is_macro = GetCompilerLibMacro(dir, SDLPathDefault, SDLPathDefaultLib);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real lib path we had computed instead
            SDLPathDefaultLib = dir_nomacro_lib;
        }
    }
    return true;
}

// return the files this project contains
function GetFilesDir()
{
    return _T("sdl/files");
}

// setup the already created project
function SetupProject(project)
{
    if (PLATFORM == PLATFORM_MSW)
    {
        project.AddIncludeDir(SDLPathDefaultInc);
        project.AddLibDir(SDLPathDefaultLib);

        // add link libraries
        project.AddLinkLib(_T("mingw32"));
        project.AddLinkLib(_T("SDLmain"));
        project.AddLinkLib(_T("SDL.dll"));
        project.AddLinkLib(_T("user32"));
        project.AddLinkLib(_T("gdi32"));
        project.AddLinkLib(_T("winmm"));
        project.AddLinkLib(_T("dxguid"));
    }
    else if (PLATFORM == PLATFORM_MAC)
    {
        //project.AddCompilerOption(_T("-D_GNU_SOURCE=1 -D_THREAD_SAFE"));
        project.AddLinkerOption(_T("-framework SDL"));
        // libSDLmain.a does not exist by default, needs to be built from SDLMain.m:
        project.AddLinkLib(_T("SDLmain"));

        project.AddLinkerOption(_T("-framework Cocoa")); // SDL dependency
    }
    else
    {
        // unix way
        project.AddCompilerOption(_T("`sdl-config --cflags`"));
        project.AddLinkerOption(_T("`sdl-config --libs`"));
    }

    // enable compiler warnings (project-wide)
    WarningsOn(project, Wizard.GetCompilerID());

    // Debug
    local target = project.GetBuildTarget(Wizard.GetDebugName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttConsoleOnly); // ttConsoleOnly: console for debugging
        target.SetOutputFilename(Wizard.GetDebugOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        // enable generation of debugging symbols for target
        DebugSymbolsOn(target, Wizard.GetCompilerID());
    }

    // Release
    target = project.GetBuildTarget(Wizard.GetReleaseName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttExecutable); // ttExecutable: no console
        target.SetOutputFilename(Wizard.GetReleaseOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        // enable optimizations for target
        OptimizationsOn(target, Wizard.GetCompilerID());
    }

    return true;
}
