<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
		<Lexer name="nVidia cg"
				index="3"
				filemasks="*.cg,*.cgfx">
				<Style name="Default"
						index="0"
						fg="0,0,0"
						bg="255,255,255"
						bold="0"
						italics="0"
						underlined="0"/>
				<Style name="Comment (normal)"
						index="1,2"
						fg="160,160,160"/>
				<Style name="Comment (documentation)"
						index="3,15"
						fg="128,128,255"
						bold="1"/>
				<Style name="Comment keyword (documentation)"
						index="17"
						fg="0,128,128"/>
				<Style name="Comment keyword error (documentation)"
						index="18"
						fg="128,0,0"/>
				<Style name="Number"
						index="4"
						fg="240,0,240"/>
				<Style name="Keyword"
						index="5"
						fg="0,0,160"
						bold="1"/>
				<Style name="cg Standard library function"
						index="16"
						fg="0,200,0"
						bold="1"/>
				<Style name="Operator"
						index="10"
						fg="255,0,0"/>
				<Style name="Breakpoint line"
						index="-2"
						bg="255,160,160"/>
				<Style name="Debugger active line"
						index="-3"
						bg="160,160,255"/>
				<Style name="Compiler error line"
						index="-4"
						bg="255,128,0"/>
        <Keywords>
                <Language index="0"
                        value="
                        float float2 float3 float4
                        float1x1 float1x2 float1x3 float1x4
                        float2x1 float2x2 float2x3 float2x4
                        float3x1 float3x2 float3x3 float3x4
                        float4x1 float4x2 float4x3 float4x4

                        half half2 half3 half4 half1x1 half1x2
                        half1x3 half1x4 half2x1 half2x2 half2x3
                        half2x4 half3x1 half3x2 half3x3 half3x4
                        half4x1 half4x2 half4x3 half4x4

                        fixed fixed2 fixed3 fixed4
                        fixed1x1 fixed1x2 fixed1x3 fixed1x4
                        fixed2x1 fixed2x2 fixed2x3 fixed2x4
                        fixed3x1 fixed3x2 fixed3x3 fixed3x4
                        fixed4x1 fixed4x2 fixed4x3 fixed4x4

                        int int2 int3 int4
                        int1x1 int1x2 int1x3 int1x4
                        int2x1 int2x2 int2x3 int2x4
                        int3x1 int3x2 int3x3 int3x4
                        int4x1 int4x2 int4x3 int4x4

                        bool bool2 bool3 bool4
                        bool1x1 bool1x2 bool1x3 bool1x4
                        bool2x1 bool2x2 bool2x3 bool2x4
                        bool3x1 bool3x2 bool3x3 bool3x4
                        bool4x1 bool4x2 bool4x3 bool4x4

                        matrix matrix2 matrix3 matrix4
                        sampler sampler1D sampler2D sampler3D samplerCUBE samplerRECT


                        POSITION BLENDWEIGHT NORMAL TANGENT BINORMAL PSIZE BLENDINDICIES
                        TEXCOORD0 TEXCOORD1 TEXCOORD2 TEXCOORD3 TEXCOORD4 TEXCOORD5 TEXCOORD6 TEXCOORD7
                        PSIZE COLOR COLOR0 COLOR1 DEPTH TESSFACTOR FOG FOGC FOGCOORDPSIZ HPOS COL0 COL1
                        BCOL0 BCOL1 TEX0 TEX1 TEX2 TEX3 TEX4 TEX5 TEX6 TEX7 CLP0 CLP1 CLP2 CLP3 CLP4 CLP5

                        ATTR0 ATTR1 ATTR2 ATTR3 ATTR4 ATTR5 ATTR6 ATTR7 ATTR8 ATTR9 ATTR10
                        ATTR11 ATTR12 ATTR13 ATTR14 ATTR15
                        s0 s1 s2 s3 s4 s5 s6 s7 s8 s9 s10 s11 s12 s13 s14 s15
                        c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c20 c21 c22
                        c23 c24 c25 c26 c27 c28 c29 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c40 c41 c42
                        c43 c44 c45 c46 c47 c48 c49 c50 c51 c52 c53 c54 c55 c56 c57 c58 c59 c60 c61 c62
                        c63 c64 c65 c66 c67 c68 c69 c70 c71 c72 c73 c74 c75 c76 c77 c78 c79 c80 c81 c82
                        c83 c84 c85 c86 c87 c88 c89 c90 c91 c92 c93 c94 c95
                        C0 C1 C2 C3 C4 C5 C6 C7 C8 C9 C10 C11 C12 C13 C14 C15 C16 C17 C18 C19 C20 C21
                        C22 C23 C24 C25 C26 C27 C28 C29 C30 C31 C32 C33 C34 C35 C36 C37 C38 C39 C40 C41
                        C42 C43 C44 C45 C46 C47 C48 C49 C50 C51 C52 C53 C54 C55 C56 C57 C58 C59 C60 C61
                        C62 C63 C64 C65 C66 C67 C68 C69 C70 C71 C72 C73 C74 C75 C76 C77 C78 C79 C80 C81
                        C82 C83 C84 C85 C86 C87 C88 C89 C90 C91 C92 C93 C94 C95 C96 C97 C98 C99 C100
                        C101 C102 C103 C104 C105 C106 C107 C108 C109 C110 C111 C112 C113 C114 C115 C116
                        C117 C118 C119 C120 C121 C122 C123 C124 C125 C126 C127 C128 C129 C130 C131 C132
                        C133 C134 C135 C136 C137 C138 C139 C140 C141 C142 C143 C144 C145 C146 C147 C148
                        C149 C150 C151 C152 C153 C154 C155 C156 C157 C158 C159 C160 C161 C162 C163 C164
                        C165 C166 C167 C168 C169 C170 C171 C172 C173 C174 C175 C176 C177 C178 C179 C180
                        C181 C182 C183 C184 C185 C186 C187 C188 C189 C190 C191 C192 C193 C194 C195 C196
                        C197 C198 C199 C200 C201 C202 C203 C204 C205 C206 C207 C208 C209 C210 C211 C212
                        C213 C214 C215 C216 C217 C218 C219 C220 C221 C222 C223 C224 C225 C226 C227 C228
                        C229 C230 C231 C232 C233 C234 C235 C236 C237 C238 C239 C240 C241 C242 C243 C244
                        C245 C246 C247 C248 C249 C250 C251 C252 C253 C254 C255
                        register glstate

                        auto bool break case catch char class column major compile const
                        const_cast continue default delete discard do double dynamic_cast
                        else emit enum explicit extern false for friend get goto if in
                        inline inout interface long mutable namespace new operator out
                        packed private protected public register reinterpret_cast return
                        row major sampler sampler_state sampler1D sampler2D sampler3D
                        samplerCUBE shared short signed sizeof static static_cast struct
                        switch template texture1D texture2D texture3D textureCUBE
                        textureRECT this throw true try typedef typeid typename uniform
                        union unsigned using vector virtual void volatile while
                        "/>

                <User index="1"
                      value="
                      abs acos all any asin atan atan2 ceil clamp cos cosh cross degress dot exp exp2 floor
                      fmod frac frexp isfinite isinf isnan ldexp lerp lit log log2 log10 max min modf mul
                      noise pow radians round rsqrt sign sin sincos sinh smoothstep step sqrt tan tanh
                      transpose distance faceforward length normalize reflect refract
                      tex1D tex1Dproj tex2D tex2Dproj texRECT texRECTproj tex3D tex3Dproj texCUBE texCUBEproj
                      ddx ddy debug "/>

                <Documentation index="2"
                        value="a addindex addtogroup anchor arg attention
                        author b brief bug c class code date def defgroup deprecated dontinclude
                        e em endcode endhtmlonly endif endlatexonly endlink endverbatim enum example exception
                        f$ f[ f] file fn hideinitializer htmlinclude htmlonly
                        if image include ingroup internal invariant interface latexonly li line link
                        mainpage name namespace nosubgrouping note overload
                        p page par param post pre ref relates remarks return retval
                        sa section see showinitializer since skip skipline struct subsection
                        test throw todo typedef union until
                        var verbatim verbinclude version warning weakgroup $ @ \ &amp; &lt; &gt; # { }"/>
        </Keywords>
        <SampleCode value="lexer_cg.sample" error_line="23"/>
        <LanguageAttributes
            LineComment="//"
            StreamCommentStart="/*"
            StreamCommentEnd="*/"
            BoxCommentStart="/* "
            BoxCommentMid=" * "
            BoxCommentEnd=" */"
            CaseSensitive="1"
            LexerCommentStyles="1,2,3,15,17,18,23,65,66,67,79,81,82,87"
            LexerCharacterStyles="7,71"
            LexerStringStyles="6,12,70,76"
            LexerPreprocessorStyles="9,73"/>
		</Lexer>
</CodeBlocks_lexer_properties>
