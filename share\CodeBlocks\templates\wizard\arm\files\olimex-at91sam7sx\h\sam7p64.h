#ifndef __OLIMEX_AT91SAM7P64_H__
#define __OLIMEX_AT91SAM7P64_H__
/*====================================================================
* Project: Board Support Package (BSP)
* Developed using:
* Function: Frequencies on Olimex-AT91SAM7S64 board
*
* Copyright HighTec EDV-Systeme GmbH 1982-2006
*====================================================================*/


#include "at91sam7sx.h"

#define FREQ_OSC		18423000	/* External oscillator frequency */
#define FREQ_PLL		95953125	/* PLL output frequency (MUL=124,DIV=24) */
#define FREQ_MCK		47976562	/* CPU frequency */


#endif /* __OLIMEX_AT91SAM7P64_H__ */
