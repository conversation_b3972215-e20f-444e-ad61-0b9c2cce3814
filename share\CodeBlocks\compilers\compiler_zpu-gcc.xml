﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler>
<CodeBlocks_compiler name="GNU GCC Compiler for ZPU"
                     id="zpu-gcc"
                     weight="60">
    <Path type="master">
        <Search envVar="PATH"
                for="C"/>
        <if platform="windows">
            <Search path="C:\zpu-gcc*"/>
            <Fallback path="C:\zpu-gcc"/>
        </if>
        <else>
            <Fallback path="/usr"/>
        </else>
    </Path>
    <if platform="windows">
        <Path type="include">
            <Add><master/>\zpu\include</Add>
        </Path>
        <Path type="lib">
            <Add><master/>\zpu\lib</Add>
        </Path>
    </if>
    <else>
        <Path type="include">
            <Add><master/>/include</Add>
        </Path>
        <Path type="lib">
            <Add><master/>/lib</Add>
        </Path>
    </else>
</CodeBlocks_compiler>
