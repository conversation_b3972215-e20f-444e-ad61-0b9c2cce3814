<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="5" />
    <object class="Project" expanded="1">
        <property name="bitmaps"></property>
        <property name="code_generation">C++</property>
        <property name="encoding">UTF-8</property>
        <property name="file">GUIDialog</property>
        <property name="first_id">1000</property>
        <property name="icons"></property>
        <property name="internationalize">0</property>
        <property name="name">MyProject</property>
        <property name="path">.</property>
        <property name="precompiled_header">wx/wxprec.h</property>
        <property name="relative_path">1</property>
        <property name="use_enum">1</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Dialog" expanded="1">
            <property name="bg"></property>
            <property name="center"></property>
            <property name="enabled">1</property>
            <property name="extra_style"></property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">GUIDialog</property>
            <property name="pos"></property>
            <property name="size"></property>
            <property name="style">wxDEFAULT_DIALOG_STYLE</property>
            <property name="subclass"></property>
            <property name="title">wxWidgets Application Template</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_style"></property>
            <event name="OnClose">OnClose</event>
            <event name="OnSize"></event>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bSizer1</property>
                <property name="orient">wxHORIZONTAL</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxALL|wxEXPAND</property>
                    <property name="proportion">0</property>
                    <object class="wxStaticText" expanded="1">
                        <property name="bg"></property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="font">Arial,90,90,20</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Welcome To&#x0A;wxWidgets</property>
                        <property name="maximum_size"></property>
                        <property name="minimum_size"></property>
                        <property name="name">m_staticText1</property>
                        <property name="permission">protected</property>
                        <property name="pos"></property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_style"></property>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND</property>
                    <property name="proportion">1</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">bSizer2</property>
                        <property name="orient">wxVERTICAL</property>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL</property>
                            <property name="proportion">0</property>
                            <object class="wxButton" expanded="1">
                                <property name="bg"></property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="font"></property>
                                <property name="hidden">0</property>
                                <property name="id">idBtnAbout</property>
                                <property name="label">&amp;About</property>
                                <property name="maximum_size"></property>
                                <property name="minimum_size"></property>
                                <property name="name">BtnAbout</property>
                                <property name="permission">protected</property>
                                <property name="pos"></property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass"></property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">OnAbout</event>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL|wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="wxStaticLine" expanded="1">
                                <property name="bg"></property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="font"></property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="maximum_size"></property>
                                <property name="minimum_size"></property>
                                <property name="name">m_staticline1</property>
                                <property name="permission">protected</property>
                                <property name="pos"></property>
                                <property name="size"></property>
                                <property name="style">wxLI_HORIZONTAL</property>
                                <property name="subclass"></property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_style"></property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL</property>
                            <property name="proportion">0</property>
                            <object class="wxButton" expanded="1">
                                <property name="bg"></property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="font"></property>
                                <property name="hidden">0</property>
                                <property name="id">idBtnQuit</property>
                                <property name="label">&amp;Quit</property>
                                <property name="maximum_size"></property>
                                <property name="minimum_size"></property>
                                <property name="name">BtnQuit</property>
                                <property name="permission">protected</property>
                                <property name="pos"></property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass"></property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">OnQuit</event>
                            </object>
                        </object>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
