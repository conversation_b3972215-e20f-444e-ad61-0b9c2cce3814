# TexTools for Blender #

TexTools is a Free addon for Blender 3D with a set of professional UV and Texture tools. Back in 2009 renderhjs released the [Original TexTools](http://renderhjs.net/textools/) for 3dsMax. Current features include: multiple out-of-the-box Texture Baking modes, UV Layout tools (Align, Rectify, Sort, Randomize...), Texel Density tools, smart UV Selection operators, Color ID tools and some UV related Mesh creation utilities.

## Download & Documentation ##
Visit the [Official Website & Documentation](http://renderhjs.net/textools/blender/) for an in depth overview of the original tools (outdated).

## Links ##
* Blenderartists [discussion thread](https://blenderartists.org/forum/showthread.php?443182-TexTools-for-Blender)
* Original author [renderhjs.net](http://www.renderhjs.net/) personal website, all written in haxe ;)
* renderhjs's [Git repository](https://bitbucket.org/renderhjs/textools-blender) on BitBucket
* renderhjs's [release log](http://renderhjs.net/textools/blender/log.html)
* renderhjs's [3dsMax version](http://renderhjs.net/textools/) of TexTools
* Polycount [discussion thread](http://polycount.com/discussion/197226/textools-for-blender)

---

## Installation ##

1. Download TexTools for Blender from [master](https://github.com/SavMartin/TexTools-Blender/archive/master.zip)(best), or the latest release.
2. In Blender from the **File** menu open **User Preferences** ![](http://renderhjs.net/textools/blender/img/installation_open_preferences.png) 
3. Go to the **Add-ons** tab ![](http://renderhjs.net/textools/blender/img/installation_addons.png).
4. Look for any old version of TexTools currently installed and uninstall it.
5. Hit **Install Addon-on from File...** ![](http://renderhjs.net/textools/blender/img/installation_install_addon_from_file.png) and Select the zip file.
6. Enable the TexTools Addon.
7. The TexTools panel can be found in the **UV/Image Editor** view ![](http://renderhjs.net/textools/blender/img/installation_uv_image_editor.png) in the left side Panel.
