////////////////////////////////////////////////////////////////////////////////
//
// Direct/X project wizard
//
////////////////////////////////////////////////////////////////////////////////

// globals
DirectXPath <- _T("");
DirectXSel <- 1;  // To hold selection
DirectXVerStr <- _T("9");
DirectXVer  <- 9;

function BeginWizard()
{
    local intro_msg = _T("Welcome to the new Direct/X project wizard!\n\n" +
                         "This wizard will guide you to create a new project\n" +
                         "using the Direct/X extensions.\n\n" +
                         "This wizard expects the global variable \"dx\" to point\n" +
                         "to the appropriate DirectX SDK.\n" +
                         "This is the MS DirectX SDK for a Visual C++ Toolkit 2003 project\n" +
                         "and e.g. the DevPack folder for a GCC project.\n\n" +
                         "For a Visual C++ Toolkit 2003 project in addition the global variable\n" +
                         "\"psdk\" must point to the directory of the MS platform SDK.\n\n" +
                         "You will be asked to setup the variables accordingly. If this is\n" +
                         "not the case, verify \"Settings->Global variables\"\n" +
                         "after this wizard has completed.\n\n" +
                         "When you're ready to proceed, please click \"Next\"...");

    local dxpath_descr = _T("Please select the location of the Direct/X SDK on your computer.\n" +
                            "This is the top-level folder where Direct/X was installed (unpacked).\n" +
                            "To help you, this folder must contain the subfolders\n" +
                            "\"include\" and \"lib\".\n\n") +
                            "You can also use a global variable, p.e. $(#dx)\n");

    Wizard.AddInfoPage(_T("DirectXIntro"), intro_msg);
    Wizard.AddGenericSingleChoiceListPage(_T("DirectXVersionPage"), _T("Please select the DirectX version you want to use."), _T("DirectX 8;DirectX 9"), DirectXSel); // select wxwidgets version
    Wizard.AddProjectPathPage();
    Wizard.AddCompilerPage(_T(""), _T("gcc*;msvc*"), true, true);
    Wizard.AddGenericSelectPathPage(_T("DirectXPath"), dxpath_descr, _T("Please select Direct/X's location:"), _T("$(#dx)"));
}

////////////////////////////////////////////////////////////////////////////////
// Direct/X's path page
////////////////////////////////////////////////////////////////////////////////

function OnEnter_DirectXVersionPage(fwd)
{
    Wizard.SetListboxSelection(_T("GenericChoiceList"), DirectXSel);
    return true;
}

function OnLeave_DirectXVersionPage(fwd)
{
    if (fwd)
    {
        DirectXSel = Wizard.GetListboxSelection(_T("GenericChoiceList"));
        switch (DirectXSel)
        {
            case 0:
                DirectXVer = 8;
                DirectXVerStr = _T("8");
                break;
            case 1:
                DirectXVer = 9;
                DirectXVerStr = _T("9");
                break;
            default:
                DirectXVer = 9;
                DirectXVerStr = _T("9");
                break;
        }
    }
    return true;
}

////////////////////////////////////////////////////////////////////////////////
// Direct/X's path page
////////////////////////////////////////////////////////////////////////////////

function OnLeave_DirectXPath(fwd)
{
    if (fwd)
    {
        local dir            = Wizard.GetTextControlValue(_T("txtFolder")); // txtFolder is the text control in GenericSelectPathPage
        local dir_nomacro    = ReplaceMacros(dir);
        local directX_header = _T("/include/d3d") + DirectXVerStr + _T(".h");
        if (!IO.FileExists(dir_nomacro + directX_header))
        {
            ShowError(_T("The path you entered seems valid, but this wizard " +
                         "can't locate Direct/X's files in it..."));
            return false;
        }

        if (GetCompilerFactory().CompilerInheritsFrom(Wizard.GetCompilerID(), _T("msvc*")))
        {
            local psdk = ReplaceMacros(_T("$(#psdk)"));
            if (!IO.DirectoryExists(psdk))
            {
                ShowError(_T("The path to the platform SDK seems not to be valid.\n" +
                             "This setup requires the MS platform SDK..."));
                return false;
            }
        }

        DirectXPath = dir;
    }
    return true;
}

// return the files this project contains
function GetFilesDir()
{
    if ( DirectXVer==8 )
        return _T("directx/dx8");

    return _T("directx/dx9");
}

// setup the already created project
function SetupProject(project)
{
    // set project options
    local gvar = ReplaceMacros(_T("$(#dx)"));

    // set compiler/linker search paths
    // see if the Direct/X SDK matches the global var. if it does, use the var instead...
    if (gvar.Matches(DirectXPath))
    {
        project.AddIncludeDir(_T("$(#dx.include)"));
        project.AddLibDir(_T("$(#dx.lib)"));
    }
    else
    {
        project.AddIncludeDir(DirectXPath + _T("/include"));
        project.AddLibDir(DirectXPath + _T("/lib"));
        project.AddLibDir(DirectXPath + _T("/lib/x86"));
    }

    // set compiler options
    project.AddCompilerOption(_T("-DWIN32"));
    project.AddCompilerOption(_T("-DNDEBUG"));
    project.AddCompilerOption(_T("-D_WINDOWS"));
    project.AddCompilerOption(_T("-D_MBCS"));

    // set linker options
    project.AddLinkLib(_T("kernel32"));
    project.AddLinkLib(_T("user32"));
    project.AddLinkLib(_T("gdi32"));
    project.AddLinkLib(_T("winspool"));
    project.AddLinkLib(_T("comdlg32"));
    project.AddLinkLib(_T("advapi32"));
    project.AddLinkLib(_T("shell32"));
    project.AddLinkLib(_T("ole32"));
    project.AddLinkLib(_T("oleaut32"));
    project.AddLinkLib(_T("uuid"));
    project.AddLinkLib(_T("odbc32"));
    project.AddLinkLib(_T("odbccp32"));
    if ( DirectXVer==8 )
    {
        project.AddLinkLib(_T("d3d8"));
    }
    else
    {
        project.AddLinkLib(_T("d3d9"));
    }

    // set additional path's for MS VC++ Toolkit
    if (GetCompilerFactory().CompilerInheritsFrom(Wizard.GetCompilerID(), _T("msvc*")))
    {
        // set project options for MS Visual C++ Toolkit
        project.AddIncludeDir(_T("$(#psdk.include)"));
        project.AddLibDir(_T("$(#psdk.lib)"));
    }

    // enable compiler warnings (project-wide)
    WarningsOn(project, Wizard.GetCompilerID());

    // Debug
    local target = project.GetBuildTarget(Wizard.GetDebugName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttConsoleOnly); // ttConsoleOnly: console for debugging
        target.SetOutputFilename(Wizard.GetDebugOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        target.SetWorkingDir(Wizard.GetDebugOutputDir());
        // enable generation of debugging symbols for target
        DebugSymbolsOn(target, Wizard.GetCompilerID());
        if (GetCompilerFactory().CompilerInheritsFrom(Wizard.GetCompilerID(), _T("gcc")))
        {
            target.AddCompilerOption(_T("-D_DEBUG"));
            target.AddCompilerOption(_T("-ggdb")); // Build for GDB
            if ( DirectXVer==8 )
                target.AddLinkLib(_T("d3dx8"));
            else
                target.AddLinkLib(_T("d3dx9"));
        }
    }

    // Release
    target = project.GetBuildTarget(Wizard.GetReleaseName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttExecutable); // ttExecutable: no console
        target.SetOutputFilename(Wizard.GetReleaseOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        target.SetWorkingDir(Wizard.GetReleaseOutputDir());
        // enable optimizations for target
        OptimizationsOn(target, Wizard.GetCompilerID());
        if (GetCompilerFactory().CompilerInheritsFrom(Wizard.GetCompilerID(), _T("gcc")))
        {
            if ( DirectXVer==8 )
                target.AddLinkLib(_T("d3dx8"));
            else
                target.AddLinkLib(_T("d3dx9"));
        }
    }

    return true;
}
