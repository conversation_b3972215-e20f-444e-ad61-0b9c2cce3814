<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<resource xmlns="http://www.wxwidgets.org/wxxrc" version="2.5.3.0">
	<object class="wxPanel" name="processorChoice">
		<style>wxTAB_TRAVERSAL</style>
		<object class="wxBoxSizer">
			<orient>wxVERTICAL</orient>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxEXPAND</flag>
				<border>5</border>
				<object class="wxBoxSizer">
					<orient>wxHORIZONTAL</orient>
					<object class="sizeritem">
						<option>0</option>
						<flag>wxALL</flag>
						<border>5</border>
						<object class="wxStaticText" name="textChoice1">
							<label>Arduino Series</label>
							<wrap>-1</wrap>
						</object>
					</object>
					<object class="sizeritem">
						<option>1</option>
						<flag>wxALL|wxALIGN_LEFT|wxALIGN_TOP|wxEXPAND</flag>
						<border>3</border>
						<object class="wxComboBox" name="comboboxBoard">
							<style>wxCB_DROPDOWN</style>
							<value>Standard (AVR)</value>
							<content>
								<item>Standard (AVR)</item>
							</content>
						</object>
					</object>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxEXPAND|wxALL</flag>
				<border>5</border>
				<object class="wxStaticLine" name="m_staticline5">
					<style>wxLI_HORIZONTAL</style>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxALL|wxALIGN_LEFT|wxALIGN_CENTER_VERTICAL</flag>
				<border>5</border>
				<object class="wxCheckBox" name="checkboxMap">
					<tooltip>Create an extended symbol file from the .elf output file</tooltip>
					<label>Create symbol map file (.map)</label>
					<checked>0</checked>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxALL|wxALIGN_LEFT|wxALIGN_CENTER_VERTICAL</flag>
				<border>5</border>
				<object class="wxCheckBox" name="checkboxLss">
					<label>Create extended listing file (.lss)</label>
					<checked>0</checked>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxALL|wxALIGN_LEFT|wxALIGN_CENTER_VERTICAL</flag>
				<border>5</border>
				<object class="wxCheckBox" name="checkboxUpload">
					<tooltip>Automatically upload to Arudino board after project built</tooltip>
					<label>Upload to board after compiliation</label>
					<checked>0</checked>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxALL|wxALIGN_RIGHT|wxALIGN_CENTER_VERTICAL</flag>
				<border>5</border>
				<object class="wxComboBox" name="uploadPort">
					<style>wxCB_DROPDOWN</style>
					<value></value>
					<content>
						<item>COM1</item>
						<item>COM2</item>
						<item>COM3</item>
						<item>COM4</item>
						<item>COM5</item>
						<item>COM6</item>
						<item>COM7</item>
						<item>COM8</item>
						<item>COM9</item>
						<item>COM10</item>
						<item>COM11</item>
						<item>COM12</item>
						<item>COM13</item>
						<item>COM14</item>
						<item>COM15</item>
						<item>COM16</item>
						<item>COM17</item>
						<item>COM18</item>
						<item>COM19</item>
						<item>COM20</item>
						<item>COM21</item>
						<item>COM22</item>
						<item>COM23</item>
						<item>COM24</item>
						<item>COM25</item>
						<item>COM26</item>
						<item>COM27</item>
						<item>COM28</item>
						<item>COM29</item>
						<item>COM30</item>
						<item>COM31</item>
						<item>COM32</item>
					</content>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxEXPAND | wxALL</flag>
				<border>5</border>
				<object class="wxStaticLine" name="m_staticline11">
					<style>wxLI_HORIZONTAL</style>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxALL</flag>
				<border>5</border>
				<object class="wxStaticText" name="m_staticText2">
					<label>Libraries</label>
					<wrap>-1</wrap>
				</object>
			</object>
			<object class="sizeritem">
				<option>0</option>
				<flag>wxALL</flag>
				<border>5</border>
				<object class="wxCheckListBox" name="m_checkList1">
					<style>wxLB_MULTIPLE|wxLB_SORT</style>
					<size>226,100</size>
					<content>
						<item>EEPROM</item>
						<item>Ethernet</item>
						<item>Firmata</item>
						<item>Flash</item>
						<item>LCD4884</item>
						<item>SD</item>
						<item>Servo</item>
						<item>SevenSegment</item>
						<item>SoftwareSerial</item>
						<item>SPI</item>
						<item>Stepper</item>
						<item>TinyGPS</item>
						<item>Wire</item>
					</content>
				</object>
			</object>
		</object>
	</object>
</resource>
