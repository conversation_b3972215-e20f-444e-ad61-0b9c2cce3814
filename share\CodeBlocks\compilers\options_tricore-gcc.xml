﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <if platform="windows">
        <Program name="C"         value="tricore-gcc.exe"/>
        <Program name="CPP"       value="tricore-g++.exe"/>
        <Program name="LD"        value="tricore-g++.exe"/>
        <Program name="DBGconfig" value=""/>
        <Program name="LIB"       value="tricore-ar.exe"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make.exe"/>
    </if>
    <else>
        <Program name="C"         value="tricore-gcc"/>
        <Program name="CPP"       value="tricore-g++"/>
        <Program name="LD"        value="tricore-g++"/>
        <Program name="DBGconfig" value=""/>
        <Program name="LIB"       value="tricore-ar"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make"/>
    </else>

    <Switch name="includeDirs"             value="-I"/>
    <Switch name="libDirs"                 value="-L"/>
    <Switch name="linkLibs"                value="-l"/>
    <Switch name="defines"                 value="-D"/>
    <Switch name="genericSwitch"           value="-"/>
    <Switch name="objectExtension"         value="o"/>
    <Switch name="needDependencies"        value="true"/>
    <Switch name="forceCompilerUseQuotes"  value="false"/>
    <Switch name="forceLinkerUseQuotes"    value="false"/>
    <Switch name="logging"                 value="default"/>
    <Switch name="libPrefix"               value="lib"/>
    <Switch name="libExtension"            value="a"/>
    <Switch name="linkerNeedsLibPrefix"    value="false"/>
    <Switch name="linkerNeedsLibExtension" value="false"/>

    <Option name="Produce debugging symbols"
            option="-g"
            category="Debugging"
            checkAgainst="-O -O1 -O2 -O3 -Os"
            checkMessage="You have optimizations enabled. This is Not A Good Thing(tm) when producing debugging symbols..."
            supersedes="-s"/>

    <if platform="windows">
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg -lgmon"
                supersedes="-s"/>
    </if>
    <else>
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg"
                supersedes="-s"/>
    </else>

    <Common name="warnings"/>

    <Category name="General Options">
        <Option name="Output an error if same variable is declared without extern in different modules"
                option="-fno-common"/>
        <Option name="Do not allocate to an enum type only as many bytes as it needs for the declared range of possible values"
                option="-fno-short-enums"/>
        <Option name="Save intermediate files in the build directory"
                option="-save-temps"/>
    </Category>

    <Category name="Linker and startup code">
        <Option name="do not link against the default crt0.o, so you can add your own startup code (MSP430 specific)"
                option="-nocrt0"/>
        <Option name="do not link against standard system startup files"
                option="-nostartfiles"/>
        <Option name="only search library directories explicitly specified on the command line"
                option="-nostdlib"/>
    </Category>

    <Common name="optimization"/>
    <Category name="Optimization">
        <Option name="No instruction scheduling before reload"
                option="-fno-schedule-insns"
                checkAgainst="-g -ggdb"
                checkMessage="You have debugging symbols enabled. This is Not A Good Thing(tm) when optimizing..."/>
        <Option name="No instruction scheduling after reload"
                option="-fno-schedule-insns2"
                checkAgainst="-g -ggdb"
                checkMessage="You have debugging symbols enabled. This is Not A Good Thing(tm) when optimizing..."/>
    </Category>

    <Category name="TriCore architecture specific">
        <Option name="jump tables in text section"
                option="-mjumptable-in-textsection"/>
        <Option name="include C source lines into assembler output"
                option="-masm-source-lines"/>
        <Option name="base relative addresses for small data objects"
                option="-msmall-pid"/>
        <Option name="allow _bit in structures and unions"
                option="-mbits-struct-unions"/>
        <Option name="allow callee and caller in different sections"
                option="-mnocallerrors"/>
        <Option name="allocate data in aligned section"
                option="-maligned-data-sections"/>
        <Option name="enable attribute alignedaccess"
                option="-maligned-access"/>
        <Option name="use optimized single float emulation"
                option="-moptfp"/>
        <Option name="layout bitfields EABI conform"
                option="-meabi-bitfields"/>
        <Option name="EABI conform"
                option="-meabi"/>
    </Category>

    <Category name="TriCore MCU derivatives"
              exclusive="true">
        <Option name="TriCore TC1130"
                option="-mcpu=tc1130"/>
        <Option name="TriCore TC1130 BA step"
                option="-mcpu=tc1130ba"/>
        <Option name="TriCore TC1161"
                option="-mcpu=tc1161"/>
        <Option name="TriCore TC1162"
                option="-mcpu=tc1162"/>
        <Option name="TriCore TC1762"
                option="-mcpu=tc1762"/>
        <Option name="TriCore TC1765"
                option="-mcpu=tc1765"/>
        <Option name="TriCore TC1766"
                option="-mcpu=tc1766"/>
        <Option name="TriCore TC1767"
                option="-mcpu=tc1767"/>
        <Option name="TriCore TC1775"
                option="-mcpu=tc1775"/>
        <Option name="TriCore TC1792"
                option="-mcpu=tc1792"/>
        <Option name="TriCore TC1796"
                option="-mcpu=tc1796"/>
        <Option name="TriCore TC1797"
                option="-mcpu=tc1797"/>
        <Option name="TriCore TC1920"
                option="-mcpu=tc1920"/>
    </Category>

    <Command name="CompileObject"
             value="$compiler $options $includes -c $file -o $object"/>
    <Command name="GenDependencies"
             value="$compiler -MM $options -MF $dep_object -MT $object $includes $file"/>
    <Command name="CompileResource"
             value="$rescomp $res_includes $res_options -J rc -O coff -i $file -o $resource_output"/>
    <Command name="LinkConsoleExe"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <if platform="windows">
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs -mwindows"/>
        <Command name="LinkDynamic"
                 value="$linker -shared -Wl,--output-def=$def_output -Wl,--out-implib=$static_output -Wl,--dll $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </if>
    <else>
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
        <Command name="LinkDynamic"
                 value="$linker -shared $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </else>
    <Command name="LinkNative"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <Command name="LinkStatic"
             value="$lib_linker -rs $static_output $link_objects"/>
    <Common name="cmds"/>

    <Common name="re"/>

    <Common name="sort"/>
</CodeBlocks_compiler_options>
