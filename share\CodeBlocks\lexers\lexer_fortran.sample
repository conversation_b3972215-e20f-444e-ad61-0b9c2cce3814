MODULE NUMERICAL
USE DECLARATIONS
CONTAINS

SUBROUTINE ELGS (A,N,INDX)
! Subroutine to perform the partial-pivoting Gaussian elimination.
! A(N,N) is the original matrix in the input and transformed matrix
! plus the pivoting element ratios below the diagonal in the output.
! INDX(N) records the pivoting order.  Copyright (c) Tao Pang 2001.
  IMPLICIT NONE
  INTEGER, INTENT (IN) :: N
  INTEGER :: I,J,K,ITMP
  INTEGER, INTENT (OUT), DIMENSION (N) :: INDX
  REAL :: C1,PI,PI1,PJ
  REAL, INTENT (INOUT), DIMENSION (N,N) :: A
  REAL, DIMENSION (N) :: C
  WRITE(*,*) 'Calculating...'
! Initialize the index
  DO I = 1, N
    INDX(I) = I
  END DO
! Find the rescaling factors, one from each row
  DO I = 1, N
    C1= 0.0
    DO J = 1, N
      C1 = AMAX1(C1,ABS(A(I,J)))
    END DO
    C(I) = C1
  END DO
! Search the pivoting (largest) element from each column
  DO J = 1, N-1
    PI1 = 0.0
    DO I = J, N
      PI = ABS(A(INDX(I),J))/C(INDX(I))
      IF (PI.GT.PI1) THEN
        PI1 = PI
        K   = I
      ENDIF
    END DO
! Search the pivoting (largest) element from each column
! Interchange the rows via INDX(N) to record pivoting order
! etc ....
  END DO
END SUBROUTINE ELGS
END MODULE NUMERICAL
