<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_template_file>
<CodeBlocks_template_file>
		<Template name="FLTK" title="FLTK Application" category="GUI" bitmap="fltk.png">
		<Notice value="This template expects FLTK to be located at C:\fltk-1.1.6.
						If this is not the case, you will have to update the relevant
						custom variable accordingly.

						To do this, click on &quot;Project->Build options->Custom variables&quot;"
				isWarning="1"/>
		<FileSet name="s" title="Default">
			<File source="fltk-main.cpp" destination="main.cpp"/>
		</FileSet>
		<Option name="FLTK Application">
			<Project file="fltk.cbp"/>
		</Option>
	</Template>
</CodeBlocks_template_file>
