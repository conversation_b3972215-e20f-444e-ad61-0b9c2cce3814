<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_project_file>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="1"/>
	<Project>
		<Option title="FLTK Application"/>
		<Option makefile="Makefile"/>
		<Build>
			<Target title="default">
				<Option type="0"/>
			</Target>
			<Environment>
				<Variable name="FLTK_DIR" value="C:\fltk-1.1.6"/>
			</Environment>
		</Build>
		<Compiler>
			<Add option="-pedantic"/>
			<Add option="-Wall"/>
			<Add option="-mms-bitfields"/>
			<Add option="-DWIN32"/>
			<Add directory="$(FLTK_DIR)\include"/>
		</Compiler>
		<Linker>
			<Add library="fltk"/>
			<Add library="gdi32"/>
			<Add library="user32"/>
			<Add library="kernel32"/>
			<Add library="ole32"/>
			<Add library="uuid"/>
			<Add library="comctl32"/>
			<Add library="wsock32"/>
			<Add library="m"/>
			<Add directory="$(FLTK_DIR)\lib"/>
		</Linker>
	</Project>
</CodeBlocks_project_file>
