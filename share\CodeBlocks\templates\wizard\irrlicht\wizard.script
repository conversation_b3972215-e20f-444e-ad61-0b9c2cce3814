////////////////////////////////////////////////////////////////////////////////
//
// Irrlicht project wizard
//
////////////////////////////////////////////////////////////////////////////////

// globals
IrrlichtPathDefault    <- _T("$(#irrlicht)");
IrrlichtPathDefaultInc <- _T("$(#irrlicht.include)");
IrrlichtPathDefaultLib <- _T("$(#irrlicht.lib)");
IrrlichtPath <- _T("");

function BeginWizard()
{
    local intro_msg = _T("Welcome to the new Irrlicht project wizard!\n\n" +
                         "This wizard will guide you to create a new project\n" +
                         "using the Irrlicht 3D real-time engine.\n\n" +
                         "When you 're ready to proceed, please click \"Next\"...");

    local irrpath_descr = _T("Please select the location of Irrlicht on your computer.\n" +
                             "This is the top-level folder where Irrlicht was installed (unpacked).\n" +
                             "To help you, this folder must contain the subfolders\n" +
                             "\"include\" and \"lib\".");

    Wizard.AddInfoPage(_T("IrrIntro"), intro_msg);
    Wizard.AddProjectPathPage();
    Wizard.AddGenericSelectPathPage(_T("IrrPath"), irrpath_descr, _T("Please select Irrlicht's location:"), IrrlichtPathDefault);
    Wizard.AddCompilerPage(_T(""), _T("*"), true, true);
}

////////////////////////////////////////////////////////////////////////////////
// Irrlicht's path page
////////////////////////////////////////////////////////////////////////////////

function OnLeave_IrrPath(fwd)
{
    if (fwd)
    {
        local dir         = Wizard.GetTextControlValue(_T("txtFolder")); // txtFolder is the text control in GenericSelectPathPage
        local dir_nomacro = VerifyDirectory(dir);

        if (dir_nomacro.IsEmpty())
            return false;

        // verify include dependencies
        local dir_nomacro_inc = GetCompilerIncludeDir(dir, IrrlichtPathDefault, IrrlichtPathDefaultInc);
        if (dir_nomacro_inc.IsEmpty())
            return false;
        if (!VerifyFile(dir_nomacro_inc, _T("irrlicht.h"), _T("Irrlicht's include"))) return false;

        // verify library dependencies
        local dir_nomacro_lib = GetCompilerLibDir(dir, IrrlichtPathDefault, IrrlichtPathDefaultLib);
        if (dir_nomacro_lib.IsEmpty())
            return false;

        local lib_dir = dir_nomacro_lib + wxFILE_SEP_PATH;
        if (PLATFORM == PLATFORM_MSW)
        {
            if (   (!IO.FileExists(lib_dir + _T("libIrrlicht.a")))
                && (!IO.FileExists(lib_dir + _T("Win32-gcc") + wxFILE_SEP_PATH + _T("libIrrlicht.a")))
                && (!IO.FileExists(lib_dir + _T("Win32-visualstudio") + wxFILE_SEP_PATH + _T("Irrlicht.lib"))) )

            {
                ShowError(_T("The path you entered seems valid, but this wizard\n" +
                             "can't locate the Irrlicht's library file in it.\n"));
                return false;
            }

        }
        else
        {
            if (   (!IO.FileExists(lib_dir + _T("libIrrlicht.a")))
                && (!IO.FileExists(lib_dir + _T("Linux") + wxFILE_SEP_PATH + _T("libIrrlicht.a"))) )

            {
                ShowError(_T("The path you entered seems valid, but this wizard\n" +
                             "can't locate the Irrlicht's library file in it.\n"));
                return false;
            }
        }

        IrrlichtPath = dir; // Remember the original selection.

        local is_macro = _T("");

        // try to resolve the include directory as macro
        is_macro = GetCompilerIncludeMacro(dir, IrrlichtPathDefault, IrrlichtPathDefaultInc);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real inc path we had computed instead
            IrrlichtPathDefaultInc = dir_nomacro_inc;
        }

        // try to resolve the library directory as macro
        is_macro = GetCompilerLibMacro(dir, IrrlichtPathDefault, IrrlichtPathDefaultLib);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real lib path we had computed instead
            IrrlichtPathDefaultLib = dir_nomacro_lib;
        }
    }
    return true;
}

// return the files this project contains
function GetFilesDir()
{
    return _T("irrlicht/files");
}

// setup the already created project
function SetupProject(project)
{
    // set project options

    // calculate the final lib directory, based on current platform and compiler
    local IrrlichtLibDir;
    if (PLATFORM == PLATFORM_MSW)
    {
        if (GetCompilerFactory().CompilerInheritsFrom(Wizard.GetCompilerID(), _T("msvc*")))
            IrrlichtLibDir = _T("Win32-VisualStudio");
        else if (GetCompilerFactory().CompilerInheritsFrom(Wizard.GetCompilerID(), _T("gcc*")))
            IrrlichtLibDir = _T("Win32-gcc");
        else
        {
            IrrlichtLibDir = _T("Win32-gcc"); // fallback is GCC
            ShowWarning(_T("This wizard only knows how to setup a project for GCC (windows+linux) or Free Microsoft VisualC++ Toolkit 2003.\n" +
                           "Continuing but you 're on your own..."));
        }
    }
    else
        IrrlichtLibDir = _T("");

    // set compiler/linker search paths
    project.AddIncludeDir(IrrlichtPathDefaultInc);
    project.AddLibDir(IrrlichtPathDefaultLib + wxFILE_SEP_PATH + IrrlichtLibDir);

    // add link libraries
    project.AddLinkLib(_T("Irrlicht"));
    if (PLATFORM == PLATFORM_MSW)
    {
        project.AddLinkLib(_T("opengl32"));
        project.AddLinkLib(_T("glu32"));
        project.AddLinkLib(_T("gdi32"));
    }
    else
    {
        project.AddLinkLib(_T("GL"));
        project.AddLinkLib(_T("GLU"));
        project.AddLinkLib(_T("Xxf86vm"));
    }

    // enable compiler warnings (project-wide)
    WarningsOn(project, Wizard.GetCompilerID());

    // Debug
    local target = project.GetBuildTarget(Wizard.GetDebugName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttConsoleOnly); // ttConsoleOnly: console for debugging
        target.SetOutputFilename(Wizard.GetDebugOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        target.SetWorkingDir(IrrlichtPath + _T("/bin/") + IrrlichtLibDir);
        // enable generation of debugging symbols for target
        DebugSymbolsOn(target, Wizard.GetCompilerID());
    }

    // Release
    target = project.GetBuildTarget(Wizard.GetReleaseName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttExecutable); // ttExecutable: no console
        target.SetOutputFilename(Wizard.GetReleaseOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        target.SetWorkingDir(IrrlichtPath + _T("/bin/") + IrrlichtLibDir);
        // enable optimizations for target
        OptimizationsOn(target, Wizard.GetCompilerID());
    }

    return true;
}
