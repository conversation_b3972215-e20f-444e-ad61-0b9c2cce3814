<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OnlineSpellCheckingConfigurationFile>
    <Language name="Ada"                     index="5,6,7,8,10" />
    <Language name="AngelScript"             index="1,2,3,6,7,12,15" />
    <Language name="Bash"                    index="2,5,6" />
    <Language name="Batch"                   index="1" />
    <Language name="BibTeX"                  index="1,5,6" />
    <Language name="Caml"                    index="9,11,12,13,14,15" />
    <Language name="C/C++"                   index="1,2,3,6,12,15,20,23,24,65,66,67,70,76,79,87,88" />
    <Language name="CMake"                   index="1,2,3,4" />
    <Language name="CoffeeScript"            index="1,2,3,6,7,12,15,22,24" />
    <Language name="CSS"                     index="9,12,14" />
    <Language name="D"                       index="1,2,3,4,10,11,12,15,18,19" />
    <Language name="Diff/Patch"              index="1" />
    <Language name="Fortran77"               index="1,3,4,5" />
    <Language name="Fortran"                 index="1,3,4,5" />
    <Language name="GameMonkey script"       index="1,2,3,6,7,12,15,17,18" />
    <Language name="Haskell"                 index="4,5,13,14,15,16" />
    <Language name="Hitachi asm"             index="1,2,3,6,7,12,15,17,18" />
    <Language name="HTML/PHP/ASP/JS"         index="0,9,19,20,24,25,29,42,43,44,48,49,51,57,58,59,63,64,66,72,75,77,82,85,87,92,94,95,97,98,107,109,110,112,113,119,120,124,125" />
    <Language name="Java"                    index="1,2,3,6,12,15" />
    <Language name="JavaScript"              index="1,2,3,6,7,12,15" />
    <Language name="LaTeX"                   index="0,4,5" />
    <Language name="Lisp"                    index="1,6,8,12" />
    <Language name="Lua"                     index="1,2,3,6,8,12" />
    <Language name="Make"                    index="1" />
    <Language name="MASM Assembly"           index="1,3,11,12,13" />
    <Language name="Matlab"                  index="1,3,4,5" />
    <Language name="Motorola 68k"            index="1,5,13" />
    <Language name="NSIS"                    index="1,2,3,4,18" />
    <Language name="nVidia cg"               index="1,2,3,15" />
    <Language name="Ogre Compositor script"  index="1,2,3" />
    <Language name="Ogre Material script"    index="1,2,3" />
    <Language name="OpenGL Shading Language" index="1,2,3,15" />
    <Language name="Pascal"                  index="1,2,3,7" />
    <Language name="Perl"                    index="2,6,7,19,26,27,28,29,30" />
    <Language name="Plain text"              index="0" />
    <Language name="Postscript"              index="1,2,7,12" />
    <Language name="Properties"              index="1" />
    <Language name="Python"                  index="1,3,4,6,7,12,13" />
    <Language name="Ruby"                    index="2,6,7,24,25,26,27,28" />
    <Language name="Smalltalk"               index="1,3,15" />
    <Language name="SQL"                     index="1,2,3,6,7" />
    <Language name="Squirrel"                index="1,2,3,6,7,12,13,15" />
    <Language name="VBScript"                index="1,4,9,72,75,77" />
    <Language name="Verilog"                 index="1,2,3,6" />
    <Language name="VHDL"                    index="1,2,4,7,15" />
    <Language name="Windows resource"        index="1,2,3,15,17,18,6,7,12" />
    <Language name="XBase"                   index="1,2,3,4,5,6,12" />
    <Language name="XML"                     index="0,5,6,7,8,9,17" />
</OnlineSpellCheckingConfigurationFile>
