﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <Category name="Code generation">
        <Option name="Enable read-only string pooling"
                option="/GF"/>
        <Option name="Separate functions for linker"
                option="/Gy"/>
        <Option name="Enable security checks"
                option="/GS"/>
        <Option name="Enable C++ RTTI"
                option="/GR"/>
        <Option name="Enable C++ exception handling (no SEH)"
                option="/EHs"
                supersedes="/EHa"/>
        <Option name="Enable C++ exception handling (w/ SEH)"
                option="/EHa"
                supersedes="/EHs"/>
        <Option name="extern 'C' defaults to nothrow"
                option="/EHc"/>
        <Option name="Consider floating-point exceptions when generating code"
                option="/fp:except"
                supersedes="/fp:except-"/>
        <Option name="Do not consider floating-point exceptions when generating code"
                option="/fp:except-"
                supersedes="/fp:except"/>
        <Option name="'fast' floating-point model; results are less predictable"
                option="/fp:fast"
                supersedes="/fp:precise"/>
        <Option name="'precise' floating-point model; results are predictable"
                option="/fp:precise"
                supersedes="/fp:fast"/>
        <Option name="'strict' floating-point model (implies /fp:except)"
                option="/fp:strict"/>
        <Option name="Enable minimal rebuild"
                option="/Gm"/>
        <Option name="Enable link-time code generation"
                option="/GL"
                checkAgainst="/Zi /ZI"
                checkMessage="Link-time code generation is incompatible with debugging info"/>
        <Option name="Optimize for windows application"
                option="/GA"/>
        <Option name="Enable _penter function call"
                option="/Gh"/>
        <Option name="Enable _pexit function call"
                option="/GH"/>
        <Option name="Generate fiber-safe TLS accesses"
                option="/GT"/>
        <Option name="Enable fast checks (/RTCsu)"
                option="/RTC1"/>
        <Option name="Convert to smaller type checks"
                option="/RTCc"/>
        <Option name="Stack Frame runtime checking"
                option="/RTCs"/>
        <Option name="Uninitialized local usage checks"
                option="/RTCu"/>
        <Option name="__cdecl calling convention"
                option="/Gd"/>
        <Option name="__fastcall calling convention"
                option="/Gr"/>
        <Option name="__stdcall calling convention"
                option="/Gz"/>
        <Option name="Enable SSE instruction set"
                option="/arch:SSE"
                supersedes="/arch:SSE2"/>
        <Option name="Enable SSE2 instruction set"
                option="/arch:SSE2"
                supersedes="/arch:SSE"/>
    </Category>
</CodeBlocks_compiler_options>
