////////////////////////////////////////////////////////////////////////////////
//
// STLport project wizard
//
////////////////////////////////////////////////////////////////////////////////

// globals (used under windows only)
STLportPathDefault    <- _T("$(#stlport)");
STLportPathDefaultInc <- _T("$(#stlport.include)");
STLportPathDefaultLib <- _T("$(#stlport.lib)");
STLportPath           <- _T("");

function BeginWizard()
{
    local intro_msg = _T("Welcome to the new STLport project wizard!\n\n" +
                         "This wizard will guide you to create a new project\n" +
                         "using the STLport library.\n\n" +
                         "When you 're ready to proceed, please click \"Next\"...");

    Wizard.AddInfoPage(_T("STLportIntro"), intro_msg);
    Wizard.AddProjectPathPage();
    if (PLATFORM == PLATFORM_MSW)
    {
        local stlportpath_descr = _T("Please select the location of STLport on your computer.\n" +
                                     "This is the top-level folder where STLport was installed (unpacked).\n" +
                                     "To help you, this folder must contain the subfolders\n" +
                                     "\"include\" and \"lib\".\n\n") +
                                     "You can also use a global variable, p.e. $(#stlport)\n");

        Wizard.AddGenericSelectPathPage(_T("STLportPath"), stlportpath_descr, _T("Please select STLport's location:"), STLportPathDefault);
    }

    Wizard.AddCompilerPage(_T(""), _T("*"), true, true);
}

////////////////////////////////////////////////////////////////////////////////
// STLport's path page
////////////////////////////////////////////////////////////////////////////////

function OnLeave_STLportPath(fwd)
{
    if (fwd)
    {
        local dir         = Wizard.GetTextControlValue(_T("txtFolder")); // txtFolder is the text control in GenericSelectPathPage
        local dir_nomacro = VerifyDirectory(dir);

        if (dir_nomacro.IsEmpty())
            return false;

        // verify include dependencies
        local dir_nomacro_inc = GetCompilerIncludeDir(dir, STLportPathDefault, STLportPathDefaultInc);
        if (dir_nomacro_inc.IsEmpty())
            return false;
        if (!VerifyFile(dir_nomacro_inc, _T("iostream"), _T("STLport's include")))
            return false;

        // verify library dependencies
        local dir_nomacro_lib = GetCompilerLibDir(dir, STLportPathDefault, STLportPathDefaultLib);
        if (dir_nomacro_lib.IsEmpty())
            return false;

        if (!VerifyLibFile(dir_nomacro_lib, _T("stlport.5.1.dll"),     _T("STLport's")))
            return false;

        if (!VerifyLibFile(dir_nomacro_lib, _T("stlportg.5.1.dll"),    _T("STLport's")))
            return false;

        if (!VerifyLibFile(dir_nomacro_lib, _T("stlportstlg.5.1.dll"), _T("STLport's")))
            return false;

        STLportPath = dir; // Remember the original selection.

        local is_macro = _T("");

        // try to resolve the include directory as macro
        is_macro = GetCompilerIncludeMacro(dir, STLportPathDefault, STLportPathDefaultInc);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real inc path we had computed instead
            STLportPathDefaultInc = dir_nomacro_inc;
        }

        // try to resolve the library directory as macro
        is_macro = GetCompilerLibMacro(dir, STLportPathDefault, STLportPathDefaultLib);
        if (is_macro.IsEmpty())
        {
            // not possible -> use the real lib path we had computed instead
            STLportPathDefaultLib = dir_nomacro_lib;
        }
    }
    return true;
}

// return the files this project contains
function GetFilesDir()
{
    return _T("stlport/files");
}

// setup the already created project
function SetupProject(project)
{
    if (PLATFORM == PLATFORM_MSW)
    {
        project.AddIncludeDir(STLportPathDefaultInc);
        project.AddLibDir(STLportPathDefaultLib);

        project.AddCompilerOption(_T("-mthreads"));

        // add link libraries
        project.AddLinkLib(_T("stlport.5.1.dll"));
        project.AddLinkLib(_T("stlportg.5.1.dll"));
        project.AddLinkLib(_T("stlportstlg.5.1.dll"));

        project.AddLinkerOption(_T("-mthreads"));
    }

    // enable compiler warnings (project-wide)
    WarningsOn(project, Wizard.GetCompilerID());

    // Debug
    local target = project.GetBuildTarget(Wizard.GetDebugName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttConsoleOnly); // ttConsoleOnly: console for debugging
        target.SetOutputFilename(Wizard.GetDebugOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        // enable generation of debugging symbols for target
        DebugSymbolsOn(target, Wizard.GetCompilerID());
    }

    // Release
    target = project.GetBuildTarget(Wizard.GetReleaseName());
    if (!IsNull(target))
    {
        target.SetTargetType(ttExecutable); // ttExecutable: no console
        target.SetOutputFilename(Wizard.GetReleaseOutputDir() + Wizard.GetProjectName() + DOT_EXT_EXECUTABLE);
        // enable optimizations for target
        OptimizationsOn(target, Wizard.GetCompilerID());
    }

    return true;
}
