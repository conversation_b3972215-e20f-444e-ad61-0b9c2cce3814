<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
    <FileVersion major="1" minor="2" />
    <Project>
        <Option title="OGRE_Sample" />
        <Option pch_mode="0" />
        <Option compiler="0" />
        <Build>
            <Target title="Debug">
                <Option output="Debug\ogre_sample.exe" />
                <Option working_dir="$(OGRE_HOME)\bin\Debug" />
                <Option object_output="Debug\.objs" />
                <Option type="1" />
                <Option compiler="0" />
                <Option projectResourceIncludeDirsRelation="1" />
                <Compiler>
                    <Add option="$(#CB_RELEASE_TYPE)" />
                    <Add option="-DDEBUG" />
                    <Add option="-D_DEBUG" />
                </Compiler>
                <Linker>
                    <Add library="OgreMain_d" />
                    <Add directory="$(OGRE_HOME)\lib\opt\debug" />
                </Linker>
            </Target>
            <Target title="Release">
                <Option output="Release\ogre_sample.exe" />
                <Option working_dir="$(OGRE_HOME)\bin\Release" />
                <Option object_output="Release\.objs" />
                <Option type="1" />
                <Option compiler="0" />
                <Option projectResourceIncludeDirsRelation="1" />
                <Compiler>
                    <Add option="-O3" />
                    <Add option="-DNDEBUG" />
                </Compiler>
                <Linker>
                    <Add library="OgreMain" />
                    <Add directory="$(OGRE_HOME)\lib\opt\release" />
                </Linker>
            </Target>
        </Build>
        <Compiler>
            <Add directory="$(#ogre.include)" />
            <Add directory="$(#ogre.include)\opt" />
            <Add directory="$(#ogre)\Samples\include" />
        </Compiler>
        <Linker>
            <Add option="-Wl,--enable-runtime-pseudo-reloc" />
            <Add library="user32" />
            <Add library="kernel32" />
            <Add library="gdi32" />
            <Add library="opengl32" />
            <Add directory="$(#ogre.lib)" />
        </Linker>
    </Project>
</CodeBlocks_project_file>
