<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
	<Lexer name="Markdown"
		index="98"
		filemasks="*.md">
		<Style name="Default"
			index="0"
			fg="15, 15, 15"
			bg="240,250,240"
			bold="0"
			italics="0"
			underlined="0"/>
		<Style name="Line end"
			index="1"
			fg="160,160,160"
			bg="230,230,230"/>
		<Style name="Header 1"
			index="6"
			fg="30,144,255"
			bold="1"/>
		<Style name="Header 2"
			index="7"
			fg="30,144,255"
			bold="1"/>
		<Style name="Header 3"
			index="8"
			fg="30,144,255"
			bold="1"/>
		<Style name="Header 4"
			index="9"
			fg="30,144,255"
			bold="1"/>
		<Style name="Header 5"
			index="10"
			fg="30,144,255"
			bold="1"/>
		<Style name="Header 6"
			index="11"
			fg="30,144,255"
			bold="1"/>
		<Style name="Strong 1"
			index="2"
			fg="0,0,0"
			bold="1"/>
		<Style name="Strong 2"
			index="3"
			fg="0, 0, 0"
			bold="1"/>
		<Style name="EM 1"
			index="4"
			fg="0,0,0"
			italics="1"/>
		<Style name="EM 2"
			index="5"
			fg="0,0,0"
			italics="1"/>
		<Style name="Strikeout"
			index="16"
			fg="150,150,250"
			underlined="1"/>
		<Style name="HRule"
			index="17"
			fg="0,0,0"
			bg="220,230,255"
			bold="1"/>
		<Style name="Link"
			index="18"
			fg="0,0,255"
			bg="229,229,229"/>
		<Style name="UList item"
			index="13"
			fg="128,0,128"
			bg="221,208,221"/>
		<Style name="OList item"
			index="14"
			fg="128,0,128"
			bg="221,208,221"/>
		<Style name="Blockquote"
			index="15"
			fg="255,255,0"
			bg="173,216,230"/>
		<Style name="Prechar"
			index="12"
			fg="128,0,128"/>
		<Style name="Code"
			index="19"
			fg="0,0,0"
			bg="144,238,144"/>
		<Style name="Code 2"
			index="20"
			fg="240,236,0"
			bg="64,153,255"/>
		<Style name="Code BK"
			index="21"
			fg="255,0,0"/>
		<Keywords>
		<Set index="0"
			value="# ## ### #### ##### ###### [ ] ( )"/>
		<Set index="1"
			value="* ** - -- ~ ~~ + ++ *** ___ --- +++ ` `` ```"/>
		<Set index="2"
			value="&lt;!-- --&gt;"/>
		</Keywords>
		<SampleCode value="lexer_markdown.sample"/>
		<LanguageAttributes
			LineComment=""
			StreamCommentStart="&lt;!--"
			StreamCommentEnd="--&gt;"
			BoxCommentStart=""
			BoxCommentMid=""
			BoxCommentEnd=""
			CaseSensitive="0"/>
		</Lexer>
</CodeBlocks_lexer_properties>
