﻿==================================================================
YAL_EULA ver.2, friday July 1st, 2018
==================================================================


IMPORTANT.  Read the following Software License Agreement
(henceforth "Agreement") completely. By using the software, games,
or assets (henceforth "Stuff"), you indicate that you accept the terms
of the Agreement and you acknowledge that you have the authority,
for yourself or on behalf of your company, to bind to these terms. 
You may then download or install the file(s). This EULA is freely
provided for every Stuff prior to purchase and as such should not
be considered a "Click-Wrap" agreement. If you do not agree to the
terms in this Agreement, do not purchase or download the Stuff.




A human-friendly summary follows. It is supplementary and provided for
convenience to reduce verbosity, but does not replace the full text.
Details have intentionally been left out of the summary for clarity, and the
neglect to mention a detail, term or condition in the summary shall
not be construed as removal of that detail from the terms and conditions.
==================================================================
START OF THE HUMAN-READABLE SUMMARY.


If you use Stuff created by Yal, you need to follow this license agreement. If
you change your mind, you need to delete all the Stuff.

1:         Definitions.

2:         Games can be played but not resold, reposted or such. Assets can be
used to create games, books, movies and such, but cannot be distributed in
editable form. If one team member has a license to use Yal Stuff, the entire
team can use it.

3:         You can't change the license terms or license the use of Yal Stuff to
someone else (with the exception of the team member clause in section 2).
It's your responsibility to get licenses for IDEs and editors,
even if Yal Stuff require those to function.

4:         Some Yal Stuff may use open-source resources; those items maintain
their original license and need to be handled properly.

5:         It's your responsibility to get licenses for IDEs and editors, even
if Yal Stuff require those to function. If the third-party tools does something
bad, it's not Yal's fault. Don't get third-party tools in an external way.

6:         If you patent a product made with Yal Stuff, you can't sue Yal or
anyone else using Yal Stuff for patent infringement.

7:         It's your responsibility to get licenses for assets and IPs used in
your games and stuff, even if Yal Stuff uses them.

8:         If you refund the Stuff, you promise to stop using it. If you or Yal
breaks the licensing terms, the other party may end the agreement.

9:         Support from Yal is optional and not guaranteed.

10:         Yal Stuff is provided as-is and without warranty. Use at your own
risk.

11:         If you break this agreement, you can't blame Yal.

12:         If Yal Stuff does something bad, you will at most be eligible to get
the purchase price back, no matter how much damage it causes.

13:         It's your responsibility to not break international laws.

14:         Your itch.io account e-mail address will be processed for paperwork
purposes, but I won't store the data or do anything stupid with it.

15:         You can't use Yal Stuff with code that requires you to make your
source code public, such as GPL-licensed software.

16:         If you're going to take legal action against Yal, you need to do it
in Sweden.

17:         The Yal Stuff is confidental, so don't show it to people that don't
have the right to use it.

18:         You can't use Yal trademarks, logos or reputation.

19:         This document is the entire agreement, all other previous documents
(if any) are void and it's not possible to add any new terms to this agreement
later.

20:         If part of this agreement can't be enforced, it can be replaced with
a part that can or fully removed, and the rest of it still holds either way.

21:         If you break the terms and Yal doesn't do anything, that doesn't
mean it's OK to keep breaking the terms.

22:         Games and such made using Yal Stuff is considered to be derivative
of Yal Stuff, not each other.

23:         If you break the licensing terms, you can't use "I didn't understand
the licensing terms" as a legal defense.

24:         This agreement doesn't imply any business association, both sides
are independent of each other.

25:         Neither side may transfer this agreement to someone else unless the
other side thinks it's OK to do that.


END OF THE HUMAN-READABLE SUMMARY.
==================================================================




YAL STUFF END-USER LICENSE AGREEMENT


This is a legal agreement between you, as an authorized representative of your
employer, or if you have no employer, as an individual (together "you"), and
Anders Wildros, Yal, yaru.itch.io, ("Yal") and their Affiliates.  It concerns
your rights to use the Stuff identified in the Software Content Register
and provided to you in binary or source code form and any accompanying written
materials (the "Licensed Stuff"). The Licensed Stuff may include any
updates or error corrections or documentation relating to the Licensed Stuff
provided to you by Yal under this License. In consideration for Yal
allowing you to access the Licensed Stuff, you are agreeing to be bound by
the terms of this Agreement. If you do not agree to all of the terms of this
Agreement, do not download or install the Licensed Stuff. If you change your
mind later, stop using the Licensed Stuff and delete all copies of the
Licensed Stuff in your possession or control. Any copies of the Licensed
Software that you have already distributed, where permitted, and do not destroy
will continue to be governed by this Agreement. Your prior use will also
continue to be governed by this Agreement.


1.        DEFINITIONS.


1.1.                 "Affiliates" means, any corporation or entity directly or
indirectly controlled by, controlling, or under common control with Yal.


1.2.                 "Intellectual Property Rights" means any and all rights under
statute, common law or equity in and under copyrights, trade secrets, and
patents (including utility models), and analogous rights throughout the world,
including any applications for and the right to apply for, any of the
foregoing.


1.3.                 "Software Content Register" means the documentation
accompanying the Licensed Stuff which identifies the contents of the
Licensed Stuff, including but not limited to identification of any third party software.


2.        LICENSE GRANT.


2.1.                Executable Licensed Stuff (including, but not limited to, games and
software, object code) is provided for personal and recreational use only.
Unless otherwise noted, executable Licensed Stuff may not be used in commercial
derived works. If source code for executable Licensed Stuff is provided,
whether as part of the executable Licensed Stuff or as a separate product, that
source code is resource Licensed Stuff. Rendering executable Licensed Stuff unable
to execute, with means including but not limited to changing its file extension,
revoking file system execute permissions, or removing essential system libraries, does
not render the Licensed Stuff a resource Licensed Stuff. You may not translate,
reverse engineer, decompile, or disassemble the executable Licensed Stuff except to 
the extent applicable law specifically prohibits such restriction.  You must prohibit
your affiliates from translating, reverse engineering, decompiling, or disassembling the
executable Licensed Stuff, except to the extent applicable law specifically prohibits
such restriction.


2.2.                Resource Licensed Stuff (including, but not limited to, music,
sound effects, source code and graphics) may be used to create derivative works, commercial
and otherwise, provided the derivative work is in a non-resource form not intended
to be further edited by its end user (including, but not limited to, game executable,
digital or physical book, movie, software (machine-readable) object code). Credit to Yal
within a derivative work is advisory but not mandatory. The rights to intellectual
properties used in derivative works stay with their intellectual property rights 
holders.


2.3.                Licensed Stuff not clearly classifiable as executable or resources 
(including, but not limited to, documents) is provided for personal and recreational
use only.


2.4.                Provided they uphold the terms and conditions in this Agreement,
Licensed Stuff may be used by team members, subcontractors, employees and other such 
affiliates of the Licensee. This does not constitute a sublicensing or license transfer,
and the rights for using Licensed Stuff are terminated when affiliation with the Licensee
is terminated. This is not applied retroactively, thus any Derivative
Works created before such termination retains the eligibility to use Licensed Stuff.


3.        LICENSE LIMITATIONS AND RESTRICTIONS.


3.1.                 The Licensed Stuff is licensed to you, not sold.  Title to
Licensed Stuff delivered hereunder remains vested in Yal or
Yal's licensor and cannot be assigned or transferred.  You are expressly
forbidden from selling or otherwise distributing the Licensed Stuff, or any
portion thereof, except as expressly permitted herein.  This Agreement does not
grant to you any implied rights under any Yal or third party intellectual
property.


3.2.                Proprietary file format Licensed Stuff may be subject to further
terms and conditions by the vendors of the software or other tools for editing
such proprietary formats, including but not limited to restrictions on 
reverse-engineering, de-compiling, or disassembling the Stuff, or prohibition
of usage of illegal, pornographic or violent material. Even under the
fulfillment of this Agreement, such third-party Terms and conditions, including
but not limited to terms and conditions that restrict usage of material,
must be fulfilled by the User.


3.3.                Licensed Stuff may not be used to manufacture, modify, or interact with 
weapons, including but not limited to firearms, explosives, turrets or munitions.


4.           COPYLEFT.             Copyleft and Open Source materials included
in the Licensed Stuff is not licensed under the terms of this Agreement, but is instead
licensed under the terms of the applicable open source license(s), such as the
BSD License, Apache License or the GNU Lesser General Public License.
Such items are annotated and separate licensing files are provided. Your use
of the open source materials is subject to the terms of each applicable license.
You must agree to the terms of each applicable license, or you cannot use the
open source materials.


5.        PROPRIETARY REQUIREMENTS.                Licensed Stuff may be provided in
proprietary formats, including but not limited to Game Maker Studio exported
project file format. Yal is not obligated to provide non-proprietary versions
of Licensed Stuff, and use of Licensed Stuff requires lawful possession of the
proprietary tool, IDE, or licenses required to use such Licensed Stuff.
Yal may not be held liable for damages arising due to use of third-party tools
or IDEs manipulating or using Licensed Stuff, whether proprietary or otherwise.


6.           PATENT COVENANT NOT TO SUE. As partial, material consideration for the
rights granted to you under this Agreement, you covenant not to sue or
otherwise assert your patents against Yal, a Yal Affiliate or
subsidiary, or a Yal licensee of the Licensed Stuff for infringement
of your Intellectual Property Rights by the manufacture, use, sale, offer for
sale, importation or other disposition or promotion of the Licensed Stuff
and/or any redistributed portions of the Licensed Stuff.


7.                THIRD-PARTY LICENSES.  You are solely responsible for obtaining
licenses for any relevant third-party material for your use in connection with
technology that you incorporate into the your product (whether as part of the
Licensed Stuff or not), including but not limited to proprietary format file
manipulation tools, storefront API licenses, and intellectual properties featured
in derived works.


8.           TERM AND TERMINATION.   This Agreement will remain in effect unless
terminated as provided in this Section 8.


8.1.                 User is considered to have terminated this Agreement
immediately at the completion of an itch.io refund of the Licensed Stuff.


8.2.                 Either party may terminate this Agreement if the other party
is in default of any of the terms and conditions of this Agreement, and
termination is effective if the defaulting party fails to correct such default
within 30 days after written notice thereof by the non-defaulting party to the
defaulting party at the address below.


8.3.                 Notwithstanding the foregoing, Yal may terminate this
Agreement immediately upon written notice if you: breach any of your
confidentiality obligations or the license restrictions under this Agreement;
become bankrupt, insolvent, or file a petition for bankruptcy or insolvency,
make an assignment for the benefit of its creditors; enter proceedings for
winding up or dissolution ;are dissolved; or are nationalized or become subject
to the expropriation of all or substantially all of its business or assets.


8.4.                 Upon termination of this Agreement, all licenses granted under
Section 2 will expire.


8.5.                 Notwithstanding the termination of this Agreement for any
reason, the terms of Sections 1, 3, 5 through 25 will survive.


9.                       SUPPORT.  Yal is not obligated to provide any
support, upgrades or new releases of the Licensed Stuff under this
Agreement. If you wish, you may contact Yal and report problems and
provide suggestions regarding the Licensed Stuff. Yal has no
obligation to respond to such a problem report or suggestion. Yal may
make changes to the Licensed Stuff at any time, without any obligation to
notify or provide updated versions of the Licensed Stuff to you.


10.                   NO WARRANTY.  To the maximum extent permitted by law,
Yal expressly disclaims any warranty for the Licensed Stuff.  The
Licensed Stuff is provided "AS IS", without warranty of any kind, either
express or implied, including without limitation the implied warranties of
merchantability, fitness for a particular purpose, or non-infringement.  You
assume the entire risk arising out of the use or performance of the licensed
software, or any systems you design using the Licensed Stuff (if any).


11.                   INDEMNITY. You agree to fully defend and indemnify Yal
from all claims, liabilities, and costs (including reasonable attorney's fees)
related to (1) your use (including your contractors or distributee's use, if
permitted) of the Licensed Stuff or (2) your violation of the terms and
conditions of this Agreement.


12.                   LIMITATION OF LIABILITY.  EXCLUDING LIABILITY FOR A BREACH
OF SECTION 2 (LICENSE GRANTS), SECTION 3 (LICENSE LIMITATIONS AND
RESTRICTIONS), SECTION 17 (CONFIDENTIAL INFORMATION), OR CLAIMS UNDER SECTION
11(INDEMNITY), IN NO EVENT WILL EITHER PARTY BE LIABLE, WHETHER IN CONTRACT,
TORT, OR OTHERWISE, FOR ANY INCIDENTAL, SPECIAL, INDIRECT, CONSEQUENTIAL OR
PUNITIVE DAMAGES, INCLUDING, BUT NOT LIMITED TO, DAMAGES FOR ANY LOSS OF USE,
LOSS OF TIME, INCONVENIENCE, COMMERCIAL LOSS, OR LOST PROFITS, SAVINGS, OR
REVENUES, TO THE FULL EXTENT SUCH MAY BE DISCLAIMED BY LAW.  YAL'S TOTAL
LIABILITY FOR ALL COSTS, DAMAGES, CLAIMS, OR LOSSES WHATSOEVER ARISING OUT OF
OR IN CONNECTION WITH THIS AGREEMENT OR PRODUCT(S) SUPPLIED UNDER THIS
AGREEMENT IS LIMITED TO THE AGGREGATE AMOUNT PAID BY YOU TO YAL IN
CONNECTION WITH THE LICENSED STUFF TO WHICH LOSSES OR DAMAGES ARE CLAIMED.


13.                EXPORT RESTRICTIONS.       Licensed Stuff (collectively referred
to as "items") is subject to the export control laws of the United States
and other countries that may lawfully control the export of the Licensed Stuff.
Furnishing support services with respect to Licensed Stuff that is controlled as
defense or military items may also be subject to such laws.  Accordingly, you
agree you will not transfer the Licensed Stuff or furnish such services
except in compliance with the export laws of the United States and any other
country that may lawfully control the export of the Licensed Stuff or the
provision of such services.  You will indemnify and hold Yal harmless
from any claims, liabilities, damages, penalties, forfeitures, and associated
costs and expenses (including, but not limited to, attorneys' fees) that Yal may
incur due to your non-compliance with applicable export laws, rules, and regulations. 
You will immediately notify Yal of any violation of any export law, rule, or
regulation, which may affect Yal or relate to the activities covered
under this Agreement. If an export or import license, permit, or other government
required authority is required for transfer of the Licensed Stuff under this
Agreement, and such authorization is not approved, then Yal is not obligated to
proceed with the transfer until required government authorization is granted.


14.                COMPLIANCE WITH GDPR                In order to fulfill their
obligations according to this Agreement, Yal may need to process credentials
registered for the User's itch.io account by the user, including but not limited
to e-mail addresses, for purposes including, but not limited to, processing
payment and taxation data, and communication with the User for support and
information purposes. This information will not be stored by Yal, and will not
be used for any other purpose (including, but not limited to, advertising)
without prior express consent by the User. The data handled in this way is
anonymized and can not be used to obtain itch.io account information or
other personal information.


15.                SUBLICENSING UNDER GPL AND LGPL         Derived works using Licensed Stuff
may not be linked to or combined with works released under non-permissive copyleft
licenses, including but not limited to GPL and LGPL, if such linking or combination 
would necessitate disclosure and release of Licensed Stuff in editable form, including
but not limited to source code, under the terms of such licenses.


16.                  CHOICE OF LAW; VENUE.  This Agreement will be governed by,
construed, and enforced in accordance with the laws of the Kingdom of Sweden,
without regard to conflicts of laws principles, will apply to all matters
relating to this Agreement or the Licensed Stuff, and you agree that any
litigation will be subject to the exclusive jurisdiction of the state or
federal courts Stockholm, Sweden.  The United Nations Convention on
Contracts for the International Sale of Goods will not apply to this document.


17.                CONFIDENTIAL INFORMATION.  Subject to the license grants and
restrictions contained herein, you must treat the Licensed Stuff as
confidential information and you agree to retain the Licensed Stuff in
confidence perpetually, or for a period of five (5) years after the termination
of this Agreement.


18.                   TRADEMARKS.  You are not authorized to use any Yal
trademarks, brand names, or logos, nor state, suggest or otherwise imply endorsement 
by Yal, including but not limited to in written media.


19.                   ENTIRE AGREEMENT.  This Agreement constitutes the entire
agreement between you and Yal regarding the subject matter of this
Agreement, and supersedes all prior communications, negotiations,
understandings, agreements or representations, either written or oral, if any.
This Agreement may only be amended in written form, signed by you and
Yal.


20.                   SEVERABILITY.  If any provision of this Agreement is held
for any reason to be invalid or unenforceable, then the remaining provisions of
this Agreement will be unimpaired and, unless a modification or replacement of
the invalid or unenforceable provision is further held to deprive you or Yal of
a material benefit, in which case the Agreement will immediately terminate, the
invalid or unenforceable provision will be replaced with a provision that is
valid and enforceable and that comes closest to the intention underlying the
invalid or unenforceable provision.


21.                   NO WAIVER.  The waiver by Yal of any breach of any
provision of this Agreement will not operate or be construed as a waiver of
any other or a subsequent breach of the same or a different provision.


22.                        MUTUAL ANCESTRY DERIVATION.                For the
purpose of determining ancestry of a derived work, all derived works created
using Licensed Stuff are to be considered derived from the Licensed Stuff used
in their original conception. Derived works are not to be considered derived
from other derived works, whether by the same licensee or otherwise.
Modifications made to Licensed Stuff in order to produce a derived work must be
authorized by the intellectual property rights holders in possession of every
intellectual property or license used within such modifications.


23.                        IGNORANCE OF TERMS.            Failure to fully read or
understand the terms in this Agreement is not to be considered valid grounds for
failure to uphold them, except to the extent applicable law specifically prohibits
such restriction. Consent to the Agreement is considered to explicitly state that
the terms of the Agreement have been fully read and understood.


24.                   RELATIONSHIP OF THE PARTIES.         The parties are
independent contractors.  Nothing in this Agreement will be construed to create
any partnership, joint venture, or similar relationship.  Neither party is
authorized to bind the other to any obligations with third parties.


25.                   SUCCESSION AND ASSIGNMENT.   This Agreement will be binding
upon and inure to the benefit of the parties and their permitted successors and
assigns.  Neither party may assign this Agreement, or any part of this
Agreement, without the prior written approval of the other party, which
approval will not be unreasonably withheld or delayed.




THIS IS THE END OF THE DOCUMENT.
==================================================================