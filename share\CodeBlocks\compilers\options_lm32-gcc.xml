﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <if platform="windows">
        <Program name="C"         value="lm32-elf-gcc.exe"/>
        <Program name="CPP"       value="lm32-elf-g++.exe"/>
        <Program name="LD"        value="lm32-elf-g++.exe"/>
        <Program name="DBGconfig" value=""/>
        <Program name="LIB"       value="lm32-elf-ar.exe"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make.exe"/>
    </if>
    <else>
        <Program name="C"         value="lm32-elf-gcc"/>
        <Program name="CPP"       value="lm32-elf-g++"/>
        <Program name="LD"        value="lm32-elf-g++"/>
        <Program name="DBGconfig" value=""/>
        <Program name="LIB"       value="lm32-elf-ar"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make"/>
    </else>

    <Switch name="includeDirs"             value="-I"/>
    <Switch name="libDirs"                 value="-L"/>
    <Switch name="linkLibs"                value="-l"/>
    <Switch name="defines"                 value="-D"/>
    <Switch name="genericSwitch"           value="-"/>
    <Switch name="objectExtension"         value="o"/>
    <Switch name="needDependencies"        value="true"/>
    <Switch name="forceCompilerUseQuotes"  value="false"/>
    <Switch name="forceLinkerUseQuotes"    value="false"/>
    <Switch name="logging"                 value="default"/>
    <Switch name="libPrefix"               value="lib"/>
    <Switch name="libExtension"            value="a"/>
    <Switch name="linkerNeedsLibPrefix"    value="false"/>
    <Switch name="linkerNeedsLibExtension" value="false"/>

    <Option name="Produce debugging symbols"
            option="-g"
            category="Debugging"
            checkAgainst="-O -O1 -O2 -O3 -Os"
            checkMessage="You have optimizations enabled. This is Not A Good Thing(tm) when producing debugging symbols..."
            supersedes="-s"/>
    <if platform="windows">
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg -lgmon"
                supersedes="-s"/>
    </if>
    <else>
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg"
                supersedes="-s"/>
    </else>

    <Common name="warnings"/>

    <Common name="optimization"/>
    <Option name="Don't keep the frame pointer in a register for functions that don't need one"
            option="-fomit-frame-pointer"
            category="Optimization"
            checkAgainst="-g -ggdb"
            checkMessage="You have debugging symbols enabled. This is Not A Good Thing(tm) when optimizing..."/>

    <Category name="LM32 CPU architecture specific">
        <Option name="Enable barrel shift instructions"
                option="-mbarrel-shift-enabled"/>
        <Option name="Enable divide and modulus instructions"
                option="-mdivide-enabled"/>
        <Option name="Enable multiply instructions"
                option="-mmultiply-enabled"/>
        <Option name="Enable sign extend instructions"
                option="-msign-extend-enabled"/>
        <Option name="Enable user-defined instructions"
                option="-muser-enabled"/>
        <Option name="Enable instruction cache instructions"
                option="-micache-enabled"/>
        <Option name="Enable data cache instructions"
                option="-mdcache-enabled"/>
        <Option name="Enable the break instruction"
                option="-mbreak-enabled"/>
        <Option name="Enable all optional instructions"
                option="-mall-enabled"/>
    </Category>

    <Command name="CompileObject"
             value="$compiler $options $includes -c $file -o $object"/>
    <Command name="GenDependencies"
             value="$compiler -MM $options -MF $dep_object -MT $object $includes $file"/>
    <Command name="CompileResource"
             value="$rescomp -i $file -J rc -o $resource_output -O coff $res_includes"/>
    <Command name="LinkConsoleExe"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <if platform="windows">
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs -mwindows"/>
        <Command name="LinkDynamic"
                 value="$linker -shared -Wl,--output-def=$def_output -Wl,--out-implib=$static_output -Wl,--dll $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </if>
    <else>
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
        <Command name="LinkDynamic"
                 value="$linker -shared $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </else>
    <Command name="LinkStatic"
             value="$lib_linker -r -s $static_output $link_objects"/>
    <Command name="LinkNative"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <Common name="cmds"/>

    <Common name="re"/>

    <Common name="sort"/>
</CodeBlocks_compiler_options>
