<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
        <Lexer name="Hitachi asm"
                index="3"
                filemasks="*.s20">
                <Style name="Default"
                        index="0"
                        fg="0,0,0"
                        bg="255,255,255"
                        bold="0"
                        italics="0"
                        underlined="0"/>
                <Style name="Comment"
                        index="1,2,3,15,17,18"
                        fg="160,160,160"/>
                <Style name="Number"
                        index="4"
                        fg="240,0,240"/>
                <Style name="Keyword"
                        index="5,16"
                        fg="0,0,160"
                        bold="1"/>
                <Style name="String"
                        index="6,12"
                        fg="0,0,255"/>
                <Style name="Character"
                        index="7"
                        fg="224,160,0"/>
                <Style name="UUID"
                        index="8"
                        fg="0,0,0"/>
                <Style name="Preprocessor"
                        index="9"
                        fg="0,160,0"/>
                <Style name="Operator"
                        index="10"
                        fg="255,0,0"/>
                <Style name="Compiler error line"
                        index="-4"
                        bg="255,128,0"/>
                <Keywords>
                        <Language index="0"
                                value="ADD ADDS ADDX AND ANDC
                                       BAND BCLR BIAND BILD BIOR BIST BIXOR BLD BNOT BOR BSET BSR BST BTST BXOR
                                       BRA BT BF BHI BLS BCC BHS BCS BLO BNE BEQ BVC BVS BPL BMI BGE BLT BGT BLE
                                       CMP
                                       DAA DAS DEC DIVXS DIVXU DS DC
                                       EEPMOV EXTS EXTU
                                       INC
                                       JMP JSR
                                       LDC
                                       MOV MOVFPE MULXS MULXU
                                       NEG NOP NOT
                                       OR ORC
                                       POP PUSH
                                       ROTL ROTR ROTXL ROTXR RTE RTS
                                       SHAL SHAR SHLL SHLR SLEEP STC SUB SUBS SUBX
                                       TRAPA
                                       XOR XORC
                                       END PUBLIC EXTERN CASEON RSEG
                                       "/>
                </Keywords>
                <SampleCode value="lexer_hitasm.sample"
                        error_line="50"/>
                <LanguageAttributes
                    LineComment="//"
                    StreamCommentStart="/*"
                    StreamCommentEnd="*/"
                    BoxCommentStart="/* "
                    BoxCommentMid=" * "
                    BoxCommentEnd=" */"
                    CaseSensitive="1"
                    LexerCommentStyles="1,2,3,15,17,18,23,65,66,67,79,81,82,87"
                    LexerCharacterStyles="7,71"
                    LexerStringStyles="6,12,70,76"
                    LexerPreprocessorStyles="9,73"/>
        </Lexer>
</CodeBlocks_lexer_properties>
