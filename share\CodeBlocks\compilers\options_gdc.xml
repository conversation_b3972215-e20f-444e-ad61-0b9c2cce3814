﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <if platform="windows">
        <Program name="C"         value="mingw32-gdc.exe"/>
        <Program name="CPP"       value="mingw32-gdc.exe"/>
        <Program name="LD"        value="mingw32-gdc.exe"/>
        <Program name="DBGconfig" value="gdb_debugger:Default"/>
        <Program name="LIB"       value="ar.exe"/>
        <Program name="WINDRES"   value="windres.exe"/>
        <Program name="MAKE"      value="mingw32-make.exe"/>
    </if>
    <else>
        <Program name="C"         value="gdc"/>
        <Program name="CPP"       value="gdc"/>
        <Program name="LD"        value="gdc"/>
        <Program name="DBGconfig" value="gdb_debugger:Default"/>
        <Program name="LIB"       value="ar"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make"/>
    </else>

    <Switch name="includeDirs"             value="-I"/>
    <Switch name="libDirs"                 value="-L"/>
    <Switch name="linkLibs"                value="-l"/>
    <Switch name="defines"                 value="-fversion="/>
    <Switch name="genericSwitch"           value="-"/>
    <Switch name="objectExtension"         value="o"/>
    <Switch name="needDependencies"        value="true"/>
    <Switch name="forceCompilerUseQuotes"  value="false"/>
    <Switch name="forceLinkerUseQuotes"    value="false"/>
    <Switch name="logging"                 value="default"/>
    <Switch name="libPrefix"               value="lib"/>
    <Switch name="libExtension"            value="a"/>
    <Switch name="linkerNeedsLibPrefix"    value="false"/>
    <Switch name="linkerNeedsLibExtension" value="false"/>

    <Option name="Produce debugging symbols"
            option="-g"
            category="Debugging"
            checkAgainst="-O -O1 -O2 -O3 -Os"
            checkMessage="You have optimizations enabled. This is Not A Good Thing(tm) when producing debugging symbols..."
            supersedes="-s"/>
    <if platform="windows">
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg -lgmon"
                supersedes="-s"/>
    </if>
    <else>
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg"
                supersedes="-s"/>
    </else>

    <Category name="Warnings">
        <Option name="Enable all compiler warnings (overrides every other setting)"
                option="-Wall"
                supersedes="-w"/>
        <Option name="Enable standard compiler warnings"
                option="-W"/>
        <Option name="Stop compiling after first error"
                option="-Wfatal-errors"/>
        <Option name="Inhibit all warning messages"
                option="-w"
                supersedes="-Wall -W -pedantic -Wmain"/>
        <Option name="Enable warnings demanded by strict ISO C and ISO C++"
                option="-pedantic"/>
        <Option name="Treat as errors the warnings demanded by strict ISO C and ISO C++"
                option="-pedantic-errors"/>
        <Option name="Warn if main() is not conformant"
                option="-Wmain"/>
    </Category>

    <Category name="D features">
        <Option name="generate documentation"
                option="-fdoc"/>
        <Option name="allow deprecated features"
                option="-fdeprecated"/>
        <Option name="compile in debug code"
                option="-fdebug"/>
        <Option name="inline expand functions"
                option="-finline-functions"/>
        <Option name="compile release version, which means not generating code for contracts and asserts"
                option="-frelease"
                supersedes="-funittest"/>
        <Option name="compile in unittest code, also turns on asserts"
                option="-funittest"
                supersedes="-frelease"/>
    </Category>

    <Common name="optimization"/>

    <Common name="architecture"/>

    <Command name="CompileObject"
             value="$compiler $options $includes -c $file -o $object"/>
    <Command name="GenDependencies"
             value="$compiler -MM $options -MF $dep_object -MT $object $includes $file"/>
    <Command name="CompileResource"
             value="$rescomp $res_includes $res_options -J rc -O coff -i $file -o $resource_output"/>
    <Command name="LinkConsoleExe"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <if platform="windows">
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs -mwindows"/>
        <Command name="LinkDynamic"
                 value="$linker -shared -Wl,--output-def=$def_output -Wl,--out-implib=$static_output -Wl,--dll $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </if>
    <else>
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
        <Command name="LinkDynamic"
                 value="$linker -shared $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </else>
    <Command name="LinkNative"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <Command name="LinkStatic"
             value="$lib_linker -r $static_output $link_objects\nranlib $static_output"/>

    <RegEx name="Fatal error"
           type="error"
           msg="1">
        <![CDATA[FATAL:[ \t]*(.*)]]>
    </RegEx>
    <RegEx name="'Instantiated from here' info"
           type="normal"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]+([iI]nstantiated from here.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[windres.exe:[ \t]([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[windres.exe:[ \t]([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Resource compiler error (2)"
           type="error"
           msg="1">
        <![CDATA[windres.exe:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Preprocessor warning"
           type="warning"
           msg="4"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):([0-9]+):[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Preprocessor error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Compiler warning"
           type="warning"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]([Ww]arning:[ \t].*)]]>
    </RegEx>
    <RegEx name="Compiler error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[0-9]+:[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (2)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[[][{}() \t#%$~[:alnum:]&_:+/\.-]+\(.text\+[0-9A-Za-z]+\):([ \tA-Za-z0-9_:+/\.-]+):[ \t](.*)]]>
    </RegEx>
    <RegEx name="Linker error (lib not found)"
           type="error"
           msg="2"
           file="1">
        <![CDATA[.*(ld.*):[ \t](cannot find.*)]]>
    </RegEx>
    <RegEx name="Undefined reference"
           type="error"
           msg="2"
           file="1">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ \t](undefined reference.*)]]>
    </RegEx>
    <RegEx name="General warning"
           type="warning"
           msg="1">
        <![CDATA[([Ww]arning:[ \t].*)]]>
    </RegEx>
</CodeBlocks_compiler_options>
