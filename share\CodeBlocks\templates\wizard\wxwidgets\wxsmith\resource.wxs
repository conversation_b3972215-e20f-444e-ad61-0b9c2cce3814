<?xml version="1.0" encoding="utf-8" ?>
<wxsmith>
    [IF WXDIALOG]<object class="wxDialog" name="[CLASS_PREFIX]Dialog">
        <title>wxWidgets app</title>
        <object class="wxBoxSizer" variable="BoxSizer1" member="yes">
            <object class="sizeritem">
                <object class="wxStaticText" name="ID_STATICTEXT1" variable="StaticText1" member="yes">
                    <label>Welcome to&#x0A;wxWidgets</label>
                    <font>
                        <size>20</size>
                        <sysfont>wxSYS_DEFAULT_GUI_FONT</sysfont>
                    </font>
                </object>
                <flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
                <border>10</border>
                <option>1</option>
            </object>
            <object class="sizeritem">
                <object class="wxBoxSizer" variable="BoxSizer2" member="yes">
                    <orient>wxVERTICAL</orient>
                    <object class="sizeritem">
                        <object class="wxButton" name="ID_BUTTON1" variable="Button1" member="yes">
                            <label>About</label>
                            <handler function="OnAbout" entry="EVT_BUTTON" />
                        </object>
                        <flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
                        <border>4</border>
                        <option>1</option>
                    </object>
                    <object class="sizeritem">
                        <object class="wxStaticLine" name="ID_STATICLINE1" variable="StaticLine1" member="yes">
                            <size>10,-1</size>
                        </object>
                        <flag>wxALL|wxEXPAND|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
                        <border>4</border>
                    </object>
                    <object class="sizeritem">
                        <object class="wxButton" name="ID_BUTTON2" variable="Button2" member="yes">
                            <label>Quit</label>
                            <handler function="OnQuit" entry="EVT_BUTTON" />
                        </object>
                        <flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
                        <border>4</border>
                        <option>1</option>
                    </object>
                </object>
                <flag>wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
                <border>4</border>
            </object>
        </object>
    </object>[ENDIF WXDIALOG][IF WXFRAME]<object class="wxFrame" name="[CLASS_PREFIX]Frame">
        <object class="wxMenuBar" variable="MenuBar1" member="no">
            <object class="wxMenu" variable="Menu1" member="no">
                <label>&amp;File</label>
                <object class="wxMenuItem" name="idMenuQuit" variable="MenuItem1" member="no">
                    <label>Quit</label>
                    <accel>Alt-F4</accel>
                    <help>Quit the application</help>
                    <handler function="OnQuit" entry="EVT_MENU" />
                </object>
            </object>
            <object class="wxMenu" variable="Menu2" member="no">
                <label>Help</label>
                <object class="wxMenuItem" name="idMenuAbout" variable="MenuItem2" member="no">
                    <label>About</label>
                    <accel>F1</accel>
                    <help>Show info about this application</help>
                    <handler function="OnAbout" entry="EVT_MENU" />
                </object>
            </object>
        </object>
        <object class="wxStatusBar" name="ID_STATUSBAR1" variable="StatusBar1" member="yes">
            <fields>1</fields>
            <widths>-1</widths>
            <styles>wxSB_NORMAL</styles>
        </object>
    </object>[ENDIF WXFRAME]
</wxsmith>
