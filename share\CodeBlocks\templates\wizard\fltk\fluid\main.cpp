// generated by Fast Light User Interface Designer (fluid) version 1.0107

#include "main.h"
#include <iostream>

void bt_callback(Fl_Widget*, void* userdata) {
  if(userdata == "test")
    std::cout << "Testing" << std::endl;
else if (userdata == "close")
    exit(0);
}

Fl_Double_Window *window_main=(Fl_Double_Window *)0;

Fl_Input *input=(Fl_Input *)0;

Fl_Button *btnTest=(Fl_Button *)0;

Fl_Return_Button *btnClose=(Fl_Return_Button *)0;

int main(int argc, char **argv) {
  Fl_Double_Window* w;
  { Fl_Double_Window* o = window_main = new Fl_Double_Window(305, 135, "FLTK window");
    w = o;
    o->align(FL_ALIGN_CLIP|FL_ALIGN_INSIDE);
    input = new Fl_Input(145, 10, 150, 25, "Please enter value:");
    { Fl_Button* o = btnTest = new Fl_Button(10, 100, 100, 25, "Test");
      o->callback((Fl_Callback*)bt_callback, (void*)("test"));
    }
    { Fl_Return_Button* o = btnClose = new Fl_Return_Button(195, 100, 100, 25, "Close");
      o->callback((Fl_Callback*)bt_callback, (void*)("close"));
    }
    o->end();
  }
  w->show(argc, argv);
  return Fl::run();
}
