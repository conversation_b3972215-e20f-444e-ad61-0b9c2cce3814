<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_lexer_properties>
<CodeBlocks_lexer_properties>
		<Lexer name="Postscript"
				index="42"
				filemasks="*.ps">
				<Style name="Default"
						index="0"
						fg="0,0,0"
						bg="255,255,255"
						bold="0"
						italics="0"
						underlined="0"/>
                <Style name="Comment"
						index="1"
						fg="160,160,160"/>
				<Style name="DSC Comment"
                        index="2"
                        fg="16,16,16"/>
                <Style name="DSC Value"
                        index="3"
                        fg="255,0,0"/>
                <Style name="Number"
						index="4"
						fg="240,0,240"/>
				<Style name="Name"
						index="5"
						fg="0,0,160"/>
				<Style name="Instruction"
						index="6"
						fg="0,160,0"
						bold="1"/>
				<Style name="Literal"
						index="7"
						fg="224,160,0"
						bold="1"/>
				<Style name="Immeval"
                        index="8"
                        fg="0,0,128"
						bold="1"/>
                <Style name="Parent Array"
                        index="9"
                        fg="255,0,255"
                        bold="1"/>
                <Style name="Parrent Dict"
                        index="10"
                        fg="0,128,255"
                        bold="1"/>
                <Style name="Parrent Proc"
                        index="11"
                        fg="0,0,128"
                        bold="1"/>
				<Style name="Text"
						index="12"
						fg="0,0,255"/>
				<Style name="Hex String"
						index="13"
						fg="128,128,128"/>
				<Style name="Base85 string"
						index="14"
						fg="0,160,0"/>
				<Style name="Bad string character"
						index="15"
						fg="128,64,0"/>
				<Style name="Breakpoint line"
						index="-2"
						bg="255,160,160"/>
				<Style name="Debugger active line"
						index="-3"
						bg="160,160,255"/>
				<Style name="Compiler error line"
						index="-4"
						bg="255,128,0"/>
				<Keywords>
                        <!--"PS Level 1 operators"-->
                        <Set index="0"
                            value="$error = == FontDirectory StandardEncoding UserObjects abs add aload anchorsearch and arc arcn arcto array ashow astore atan awidthshow begin bind bitshift bytesavailable cachestatus ceiling charpath clear cleardictstack cleartomark clip clippath closefile closepath concat concatmatrix copy copypage cos count countdictstack countexecstack counttomark currentcmykcolor currentcolorspace currentdash currentdict currentfile currentflat currentfont currentgray currenthsbcolor currentlinecap currentlinejoin currentlinewidth currentmatrix currentmiterlimit currentpagedevice currentpoint currentrgbcolor currentscreen currenttransfer cvi cvlit cvn cvr cvrs cvs cvx def defaultmatrix definefont dict dictstack div dtransform dup echo end eoclip eofill eq erasepage errordict exch exec execstack executeonly executive exit exp false file fill findfont flattenpath floor flush flushfile for forall ge get getinterval grestore grestoreall gsave gt idetmatrix idiv idtransform if ifelse image imagemask index initclip initgraphics initmatrix inustroke invertmatrix itransform known kshow le length lineto ln load log loop lt makefont mark matrix maxlength mod moveto mul ne neg newpath noaccess nor not null nulldevice or pathbbox pathforall pop print prompt pstack put putinterval quit rand rcheck rcurveto read readhexstring readline readonly readstring rectstroke repeat resetfile restore reversepath rlineto rmoveto roll rotate round rrand run save scale scalefont search setblackgeneration setcachedevice setcachelimit setcharwidth setcolorscreen setcolortransfer setdash setflat setfont setgray sethsbcolor setlinecap setlinejoin setlinewidth setmatrix setmiterlimit setpagedevice setrgbcolor setscreen settransfer setvmthreshold show showpage sin sqrt srand stack start status statusdict stop stopped store string stringwidth stroke strokepath sub systemdict token token transform translate true truncate type ueofill undefineresource userdict usertime version vmstatus wcheck where widthshow write writehexstring writestring xcheck xor"/>
                        <!--"PS Level 2 operators"-->
                        <Set index="1"
                            value="GlobalFontDirectory ISOLatin1Encoding SharedFontDirectory UserObject arct colorimage cshow currentblackgeneration currentcacheparams currentcmykcolor currentcolor currentcolorrendering currentcolorscreen currentcolorspace currentcolortransfer currentdevparams currentglobal currentgstate currenthalftone currentobjectformat currentoverprint currentpacking currentpagedevice currentshared currentstrokeadjust currentsystemparams currentundercolorremoval currentuserparams defineresource defineuserobject deletefile execform execuserobject filenameforall fileposition filter findencoding findresource gcheck globaldict glyphshow gstate ineofill infill instroke inueofill inufill inustroke languagelevel makepattern packedarray printobject product realtime rectclip rectfill rectstroke renamefile resourceforall resourcestatus revision rootfont scheck selectfont serialnumber setbbox setblackgeneration setcachedevice2 setcacheparams setcmykcolor setcolor setcolorrendering setcolorscreen setcolorspace setcolortranfer setdevparams setfileposition setglobal setgstate sethalftone setobjectformat setoverprint setpacking setpagedevice setpattern setshared setstrokeadjust setsystemparams setucacheparams setundercolorremoval setuserparams setvmthreshold shareddict startjob uappend ucache ucachestatus ueofill ufill undef undefinefont undefineresource undefineuserobject upath ustroke ustrokepath vmreclaim writeobject xshow xyshow yshow"/>
						<!--"PS Level 3 operators"-->
                        <Set index="2"
                            value="cliprestore clipsave composefont currentsmoothness findcolorrendering setsmoothness shfill"/>
                        <!--"RIP-specific operators"-->
                        <Set index="3"
                            value=".begintransparencygroup .begintransparencymask .bytestring .charboxpath .currentaccuratecurves .currentblendmode .currentcurvejoin .currentdashadapt .currentdotlength .currentfilladjust2 .currentlimitclamp .currentopacityalpha .currentoverprintmode .currentrasterop .currentshapealpha .currentsourcetransparent .currenttextknockout .currenttexturetransparent .dashpath .dicttomark .discardtransparencygroup .discardtransparencymask .endtransparencygroup .endtransparencymask .execn .filename .filename .fileposition .forceput .forceundef .forgetsave .getbitsrect .getdevice .inittransparencymask .knownget .locksafe .makeoperator .namestring .oserrno .oserrorstring .peekstring .rectappend .runandhide .setaccuratecurves .setblendmode .setcurvejoin .setdashadapt .setdebug .setdefaultmatrix .setdotlength .setfilladjust2 .setlimitclamp .setmaxlength .setopacityalpha .setoverprintmode .setrasterop .setsafe .setshapealpha .setsourcetransparent .settextknockout .settexturetransparent .stringbreak .stringmatch .tempfile .type1decrypt .type1encrypt .type1execchar .unread arccos arcsin copydevice copyscanlines currentdevice finddevice findlibfile findprotodevice flushpage getdeviceprops getenv makeimagedevice makewordimagedevice max min putdeviceprops setdevice"/>
                        <!--"User-defined operators"-->
                        <Set index="4"
                            value=""/>
				</Keywords>
				<SampleCode value="lexer_postscript.sample"
						breakpoint_line="12"
						debug_line="14"
						error_line="18"/>
                <LanguageAttributes
                    LineComment="%"
                    StreamCommentStart=""
                    StreamCommentEnd=""
                    BoxCommentStart=""
                    BoxCommentMid=""
                    BoxCommentEnd=""
                    CaseSensitive="1"/>
		</Lexer>
</CodeBlocks_lexer_properties>
