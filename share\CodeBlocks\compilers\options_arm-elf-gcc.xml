﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <if platform="windows">
        <Program name="C"         value="arm-elf-gcc.exe"/>
        <Program name="CPP"       value="arm-elf-g++.exe"/>
        <Program name="LD"        value="arm-elf-g++.exe"/>
        <Program name="DBGconfig" value=""/>
        <Program name="LIB"       value="arm-elf-ar.exe"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make.exe"/>
    </if>
    <else>
        <Program name="C"         value="arm-elf-gcc"/>
        <Program name="CPP"       value="arm-elf-g++"/>
        <Program name="LD"        value="arm-elf-g++"/>
        <Program name="DBGconfig" value=""/>
        <Program name="LIB"       value="arm-elf-ar"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make"/>
    </else>

    <Switch name="includeDirs"             value="-I"/>
    <Switch name="libDirs"                 value="-L"/>
    <Switch name="linkLibs"                value="-l"/>
    <Switch name="defines"                 value="-D"/>
    <Switch name="genericSwitch"           value="-"/>
    <Switch name="objectExtension"         value="o"/>
    <Switch name="needDependencies"        value="true"/>
    <Switch name="forceCompilerUseQuotes"  value="false"/>
    <Switch name="forceLinkerUseQuotes"    value="false"/>
    <Switch name="logging"                 value="default"/>
    <Switch name="libPrefix"               value="lib"/>
    <Switch name="libExtension"            value="a"/>
    <Switch name="linkerNeedsLibPrefix"    value="false"/>
    <Switch name="linkerNeedsLibExtension" value="false"/>

    <Option name="Produce debugging symbols"
            option="-g"
            category="Debugging"
            checkAgainst="-O -O1 -O2 -O3 -Os"
            checkMessage="You have optimizations enabled. This is Not A Good Thing(tm) when producing debugging symbols..."
            supersedes="-s"/>

    <if platform="windows">
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg -lgmon"
                supersedes="-s"/>
    </if>
    <else>
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg"
                supersedes="-s"/>
    </else>

    <Common name="warnings"/>
    <Category name="Warnings">
        <Option name="Enable Effective-C++ warnings (thanks Scott Meyers)"
                option="-Weffc++"/>
        <Option name="Warn whenever a switch statement does not have a default case"
                option="-Wswitch-default"/>
        <Option name="Warn whenever a switch statement has an index of enumerated type and lacks a case for one or more of the named codes of that enumeration"
                option="-Wswitch-enum"/>
        <Option name="Warn if a user supplied include directory does not exist"
                option="-Wmissing-include-dirs"/>
        <Option name="Warn if a global function is defined without a previous declaration"
                option="-Wmissing-declarations"/>
        <Option name="Warn if the compiler detects that code will never be executed"
                option="-Wunreachable-code"/>
        <Option name="Warn if a function can not be inlined and it was declared as inline"
                option="-Winline"/>
        <Option name="Warn if floating point values are used in equality comparisons"
                option="-Wfloat-equal"/>
        <Option name="Warn if an undefined identifier is evaluated in an '#if' directive"
                option="-Wundef"/>
        <Option name="Warn whenever a pointer is cast such that the required alignment of the target is increased"
                option="-Wcast-align"/>
        <Option name="Warn if anything is declared more than once in the same scope"
                option="-Wredundant-decls"/>
        <Option name="Warn about unitialized variables which are initialized with themselves"
                option="-Winit-self"/>
        <Option name="Warn whenever a local variable shadows another local variable, parameter or global variable or whenever a built-in function is shadowed"
                option="-Wshadow"/>
        <Option name="Warn if a class has virtual functions but no virtual destructor"
                option="-Wnon-virtual-dtor"/>
    </Category>

    <Common name="optimization"/>
    <Option name="Don't keep the frame pointer in a register for functions that don't need one"
            option="-fomit-frame-pointer"
            category="Optimization"
            checkAgainst="-g -ggdb"
            checkMessage="You have debugging symbols enabled. This is Not A Good Thing(tm) when optimizing..."/>

    <Category name="ARM CPU architecture specific">
        <Option name="Generate stack frame even if unnecessary"
                option="-mapcs-frame"
                supersedes="-mno-apcs-frame"/>
        <Option name="-mno-apcs-frame"
                option="-mno-apcs-frame"
                supersedes="-mapcs-frame"/>
        <Option name="-mabi=apcs-gnu"
                option="-mabi=apcs-gnu"
                supersedes="-mabi=atpcs -mabi=aapcs -mabi=aapcs-linux -mabi=iwmmxt"/>
        <Option name="-mabi=atpcs"
                option="-mabi=atpcs"
                supersedes="-mabi=apcs-gnu -mabi=aapcs -mabi=aapcs-linux -mabi=iwmmxt"/>
        <Option name="-mabi=aapcs"
                option="-mabi=aapcs"
                supersedes="-mabi=apcs-gnu -mabi=atpcs -mabi=aapcs-linux -mabi=iwmmxt"/>
        <Option name="-mabi=aapcs-linux"
                option="-mabi=aapcs-linux"
                supersedes="-mabi=apcs-gnu -mabi=atpcs -mabi=aapcs -mabi=iwmmxt"/>
        <Option name="-mabi=iwmmxt"
                option="-mabi=iwmmxt"
                supersedes="-mabi=apcs-gnu -mabi=atpcs -mabi=aapcs -mabi=aapcs-linux"/>
        <Option name="-mapcs-stack-check"
                option="-mapcs-stack-check"
                supersedes="-mno-apcs-stack-check"/>
        <Option name="-mno-apcs-stack-check"
                option="-mno-apcs-stack-check"
                supersedes="-mapcs-stack-check"/>
        <Option name="-mapcs-float"
                option="-mapcs-float"
                supersedes="-mno-apcs-float"/>
        <Option name="-mno-apcs-float"
                option="-mno-apcs-float"
                supersedes="-mapcs-float"/>
        <Option name="-mapcs-reentrant"
                option="-mapcs-reentrant"
                supersedes="-mno-apcs-reentrant"/>
        <Option name="-mno-apcs-reentrant"
                option="-mno-apcs-reentrant"
                supersedes="-mapcs-reentrant"/>
        <Option name="-msched-prolog"
                option="-msched-prolog"
                supersedes="-mno-sched-prolog"/>
        <Option name="Prevent the reordering of instructions in the function prolog"
                option="-mno-sched-prolog"
                supersedes="-msched-prolog"/>
        <Option name="Generate code for little-endian processors"
                option="-mlittle-endian"
                supersedes="-mbig-endian"/>
        <Option name="Generate code for big-endian processors"
                option="-mbig-endian"
                supersedes="-mlittle-endian"/>
        <Option name="-mwords-little-endian"
                option="-mwords-little-endian"/>
        <Option name="-msoft-float"
                option="-msoft-float"
                supersedes="-mhard-float"/>
        <Option name="-mhard-float"
                option="-mhard-float"
                supersedes="-msoft-float"/>
        <Option name="-mfpe"
                option="-mfpe"/>
        <Option name="Generate code supporting calls between the ARM and Thumb instruction sets"
                option="-mthumb-interwork"
                supersedes="-mno-thumb-interwork -marm -mthumb"/>
        <Option name="-mno-thumb-interwork"
                option="-mno-thumb-interwork"
                supersedes="-mthumb-interwork"/>
        <Option name="'fpa' floating point unit"
                option="-mfpu=fpa"
                supersedes="-mfpu=fpe2 -mfpu=fpe3 -mfpu=maverick -mfpu=vfp -mfpu=vfpv3 -mfpu=vfpv3-d16 -mfpu=neon"/>
        <Option name="'fpe2' floating point unit"
                option="-mfpu=fpe2"
                supersedes="-mfpu=fpa -mfpu=fpe3 -mfpu=maverick -mfpu=vfp -mfpu=vfpv3 -mfpu=vfpv3-d16 -mfpu=neon"/>
        <Option name="'fpe3' floating point unit"
                option="-mfpu=fpe3"
                supersedes="-mfpu=fpa -mfpu=fpe2 -mfpu=maverick -mfpu=vfp -mfpu=vfpv3 -mfpu=vfpv3-d16 -mfpu=neon"/>
        <Option name="'maverick' floating point unit"
                option="-mfpu=maverick"
                supersedes="-mfpu=fpa -mfpu=fpe2 -mfpu=fpe3 -mfpu=vfp -mfpu=vfpv3 -mfpu=vfpv3-d16 -mfpu=neon"/>
        <Option name="'vfp' floating point unit"
                option="-mfpu=vfp"
                supersedes="-mfpu=fpa -mfpu=fpe2 -mfpu=fpe3 -mfpu=maverick -mfpu=vfpv3 -mfpu=vfpv3-d16 -mfpu=neon"/>
        <Option name="'vfpv3' floating point unit"
                option="-mfpu=vfpv3"
                supersedes="-mfpu=fpa -mfpu=fpe2 -mfpu=fpe3 -mfpu=maverick -mfpu=vfp -mfpu=vfpv3-d16 -mfpu=neon"/>
        <Option name="'vfpv3-d16' floating point unit"
                option="-mfpu=vfpv3-d16"
                supersedes="-mfpu=fpa -mfpu=fpe2 -mfpu=fpe3 -mfpu=maverick -mfpu=vfp -mfpu=vfpv3 -mfpu=neon"/>
        <Option name="'neon' floating point unit"
                option="-mfpu=neon"
                supersedes="-mfpu=fpa -mfpu=fpe2 -mfpu=fpe3 -mfpu=maverick -mfpu=vfp -mfpu=vfpv3 -mfpu=vfpv3-d16"/>
        <Option name="Round structure and union size up to a multiple of 8 bits"
                option="-mstructure-size-boundary=8"
                supersedes="-mstructure-size-boundary=32 -mstructure-size-boundary=64"/>
        <Option name="Round structure and union size up to a multiple of 32 bits"
                option="-mstructure-size-boundary=32"
                supersedes="-mstructure-size-boundary=8 -mstructure-size-boundary=64"/>
        <Option name="Round structure and union size up to a multiple of 64 bits"
                option="-mstructure-size-boundary=64"
                supersedes="-mstructure-size-boundary=8 -mstructure-size-boundary=32"/>
        <Option name="Generate a call to the function abort at the end of a noreturn function"
                option="-mabort-on-noreturn"/>
        <Option name="-mlong-calls"
                option="-mlong-calls"
                supersedes="-mno-long-calls"/>
        <Option name="-mno-long-calls"
                option="-mno-long-calls"
                supersedes="-mlong-calls"/>
        <Option name="Treat the register used for PIC addressing as read-only"
                option="-msingle-pic-base"
                supersedes="-mno-single-pic-base"/>
        <Option name="-mno-single-pic-base"
                option="-mno-single-pic-base"
                supersedes="-msingle-pic-base"/>
        <Option name="-mpic-register=R9"
                option="-mpic-register=R9"
                supersedes="-mpic-register=R10"/>
        <Option name="-mpic-register=R10"
                option="-mpic-register=R10"
                supersedes="-mpic-register=R9"/>
        <Option name="-mnop-fun-dllimport"
                option="-mnop-fun-dllimport"/>
        <Option name="-mcirrus-fix-invalid-insns"
                option="-mcirrus-fix-invalid-insns"
                supersedes="-mno-cirrus-fix-invalid-insns"/>
        <Option name="-mno-cirrus-fix-invalid-insns"
                option="-mno-cirrus-fix-invalid-insns"
                supersedes="-mcirrus-fix-invalid-insns"/>
        <Option name="-mpoke-function-name"
                option="-mpoke-function-name"/>
        <Option name="Generate code that executes in Thumb state"
                option="-mthumb"
                supersedes="-marm -mthumb-interwork"/>
        <Option name="Generate code that executes in ARM state"
                option="-marm"
                supersedes="-mthumb -mthumb-interwork"/>
        <Option name="-mtpcs-frame"
                option="-mtpcs-frame"/>
        <Option name="-mtpcs-leaf-frame"
                option="-mtpcs-leaf-frame"/>
        <Option name="-mcaller-super-interworking"
                option="-mcaller-super-interworking"/>
        <Option name="-mcallee-super-interworking"
                option="-mcallee-super-interworking"/>
    </Category>

    <Category name="ARM CPU architecture derivatives"
              exclusive="true">
        <Option name="ARM2 processor"
                option="-mcpu=arm2"/>
        <Option name="ARM250 processor"
                option="-mcpu=arm250"/>
        <Option name="ARM3 processor"
                option="-mcpu=arm3"/>
        <Option name="ARM6 processor"
                option="-mcpu=arm6"/>
        <Option name="ARM60 processor"
                option="-mcpu=arm60"/>
        <Option name="ARM600 processor"
                option="-mcpu=arm600"/>
        <Option name="ARM610 processor"
                option="-mcpu=arm610"/>
        <Option name="ARM620 processor"
                option="-mcpu=arm620"/>
        <Option name="ARM7 processor"
                option="-mcpu=arm7"/>
        <Option name="ARM7m processor"
                option="-mcpu=arm7m"/>
        <Option name="ARM7d processor"
                option="-mcpu=arm7d"/>
        <Option name="ARM7dm processor"
                option="-mcpu=arm7dm"/>
        <Option name="ARM7di processor"
                option="-mcpu=arm7di"/>
        <Option name="ARM7dmi processor"
                option="-mcpu=arm7dmi"/>
        <Option name="ARM70 processor"
                option="-mcpu=arm70"/>
        <Option name="ARM700 processor"
                option="-mcpu=arm700"/>
        <Option name="ARM700i processor"
                option="-mcpu=arm700i"/>
        <Option name="ARM710 processor"
                option="-mcpu=arm710"/>
        <Option name="ARM710c processor"
                option="-mcpu=arm710c"/>
        <Option name="ARM7100 processor"
                option="-mcpu=arm7100"/>
        <Option name="ARM720 processor"
                option="-mcpu=arm720"/>
        <Option name="ARM7500 processor"
                option="-mcpu=arm7500"/>
        <Option name="ARM7500fe processor"
                option="-mcpu=arm7500fe"/>
        <Option name="ARM7tdmi processor"
                option="-mcpu=arm7tdmi"/>
        <Option name="ARM7tdmi-s processor"
                option="-mcpu=arm7tdmi-s"/>
        <Option name="ARM710t processor"
                option="-mcpu=arm710t"/>
        <Option name="ARM720t processor"
                option="-mcpu=arm720t"/>
        <Option name="ARM740t processor"
                option="-mcpu=arm740t"/>
        <Option name="strongarm processor"
                option="-mcpu=strongarm"/>
        <Option name="strongarm110 processor"
                option="-mcpu=strongarm110"/>
        <Option name="strongarm1100 processor"
                option="-mcpu=strongarm1100"/>
        <Option name="strongarm1110 processor"
                option="-mcpu=strongarm1110"/>
        <Option name="ARM8 processor"
                option="-mcpu=arm8"/>
        <Option name="ARM810 processor"
                option="-mcpu=arm810"/>
        <Option name="ARM9 processor"
                option="-mcpu=arm9"/>
        <Option name="ARM9e processor"
                option="-mcpu=arm9e"/>
        <Option name="ARM920 processor"
                option="-mcpu=arm920"/>
        <Option name="ARM920t processor"
                option="-mcpu=arm920t"/>
        <Option name="ARM922t processor"
                option="-mcpu=arm922t"/>
        <Option name="ARM946e-s processor"
                option="-mcpu=arm946e-s"/>
        <Option name="ARM966e-s processor"
                option="-mcpu=arm966e-s"/>
        <Option name="ARM968e-s processor"
                option="-mcpu=arm968e-s"/>
        <Option name="ARM926ej-s processor"
                option="-mcpu=arm926ej-s"/>
        <Option name="ARM940t processor"
                option="-mcpu=arm940t"/>
        <Option name="ARM9tdmi processor"
                option="-mcpu=arm9tdmi"/>
        <Option name="ARM10tdmi processor"
                option="-mcpu=arm10tdmi"/>
        <Option name="ARM1020t processor"
                option="-mcpu=arm1020t"/>
        <Option name="ARM1026ej-s processor"
                option="-mcpu=arm1026ej-s"/>
        <Option name="ARM10e processor"
                option="-mcpu=arm10e"/>
        <Option name="ARM1020e processor"
                option="-mcpu=arm1020e"/>
        <Option name="ARM1022e processor"
                option="-mcpu=arm1022e"/>
        <Option name="ARM1136j-s processor"
                option="-mcpu=arm1136j-s"/>
        <Option name="ARM1136jf-s processor"
                option="-mcpu=arm1136jf-s"/>
        <Option name="mpcore processor"
                option="-mcpu=mpcore"/>
        <Option name="mpcorenovfp processor"
                option="-mcpu=mpcorenovfp"/>
        <Option name="ARM1156t2-s processor"
                option="-mcpu=arm1156t2-s"/>
        <Option name="ARM1176jz-s processor"
                option="-mcpu=arm1176jz-s"/>
        <Option name="ARM1176jzf-s processor"
                option="-mcpu=arm1176jzf-s"/>
        <Option name="cortex-a8 processor"
                option="-mcpu=cortex-a8"/>
        <Option name="cortex-a9 processor"
                option="-mcpu=cortex-a9"/>
        <Option name="cortex-r4 processor"
                option="-mcpu=cortex-r4"/>
        <Option name="cortex-r4f processor"
                option="-mcpu=cortex-r4f"/>
        <Option name="cortex-m3 processor"
                option="-mcpu=cortex-m3"/>
        <Option name="cortex-m1 processor"
                option="-mcpu=cortex-m1"/>
        <Option name="xscale processor"
                option="-mcpu=xscale"/>
        <Option name="iwmmxt processor"
                option="-mcpu=iwmmxt"/>
        <Option name="iwmmxt2 processor"
                option="-mcpu=iwmmxt2"/>
        <Option name="ep9312 processor"
                option="-mcpu=ep9312"/>
    </Category>

    <Command name="CompileObject"
             value="$compiler $options $includes -c $file -o $object"/>
    <Command name="GenDependencies"
             value="$compiler -MM $options -MF $dep_object -MT $object $includes $file"/>
    <Command name="CompileResource"
             value="$rescomp $res_includes $res_options -J rc -O coff -i $file -o $resource_output"/>
    <Command name="LinkConsoleExe"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <if platform="windows">
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs -mwindows"/>
        <Command name="LinkDynamic"
                 value="$linker -shared -Wl,--output-def=$def_output -Wl,--out-implib=$static_output -Wl,--dll $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </if>
    <else>
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
        <Command name="LinkDynamic"
                 value="$linker -shared $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </else>
    <Command name="LinkNative"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <Command name="LinkStatic"
             value="$lib_linker -r -s $static_output $link_objects"/>
    <Common name="cmds"/>

    <Common name="re"/>

    <Common name="sort"/>
</CodeBlocks_compiler_options>
