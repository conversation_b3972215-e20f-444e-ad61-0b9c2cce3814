////////////////////////////////////////////////////////////////////////////////
//
// Code::Blocks new file wizard script
//
// Project: C/C++ source file
// Author:  <PERSON><PERSON><PERSON>
//
////////////////////////////////////////////////////////////////////////////////

function BeginWizard()
{
    // this is the text that will appear in the start (intro) page
    local intro_msg = _T("Welcome to the new C/C++ source file wizard!\n" +
                         "This wizard will guide you to create a new C/C++ source file.\n\n" +
                         "When you 're ready to proceed, please click \"Next\"...");

    // add builtin pages
    Wizard.AddInfoPage(_T("CFileIntro"), intro_msg); // intro
    Wizard.AddGenericSingleChoiceListPage(_T("CFileLanguagePage"), _T("Please select the language for the file."), _T("C;C++"), 1); // select language
    Wizard.AddFilePathPage(false); // select filename (no header guard for source files)
}

function OnLeave_CFileLanguagePage(fwd)
{
    if (fwd)
    {
        if (Wizard.GetListboxSelection(_T("GenericChoiceList")) == 0)
            Wizard.SetFilePathSelectionFilter(_T("C files (*.c;*.C)|*.c;*.C")); // C
        else
            Wizard.SetFilePathSelectionFilter(_T("C++ files (*.cpp;*.cxx;*.cc)|*.cpp;*.cxx;*.cc")); // C++
    }
    return true; // allow it
}

function CreateFiles()
{
    local fname = Wizard.GetFileName();
    local ed    = GetEditorManager();
    if (IsNull(ed))
    {
        ShowError(_T("The wizard could not locate the editor manager."));
    }

    local ed_new = ed.New(fname);
    if (IsNull(ed_new))
    {
        ShowError(_T("The wizard could not create a new file.\n" +
                     "Maybe the target folder is write-protected?"));
    }
    else
    {
        // succeeded -> add file to project if needed
        if (Wizard.GetFileAddToProject())
        {
            AddFileToTargets(Wizard, fname);
        }
    }
    return fname;
}
