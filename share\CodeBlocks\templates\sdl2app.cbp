<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_project_file>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="1"/>
	<Project>
		<Option title="SDL2 Application"/>
		<Option makefile="Makefile"/>
		<Option makefile_is_custom="0"/>
		<Option compiler="0"/>
		<Build>
			<Target title="default">
				<Option output="SDL2app.exe"/>
				<Option working_dir="."/>
				<Option object_output=".objs"/>
				<Option deps_output=".deps"/>
				<Option external_deps=""/>
				<Option type="1"/>
				<Option compiler="0"/>
				<Option projectResourceIncludeDirsRelation="1"/>
			</Target>
		</Build>
		<Linker>
			<Add library="mingw32"/>
			<Add library="SDL2main"/>
			<Add library="SDL2"/>
		</Linker>
	</Project>
</CodeBlocks_project_file>
