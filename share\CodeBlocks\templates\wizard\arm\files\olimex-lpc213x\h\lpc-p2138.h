#ifndef __OLIMEX_LPC_P2138_H__
#define __OLIMEX_LPC_P2138_H__
/*====================================================================
* Project: Board Support Package (BSP)
* Developed using:
* Function: Frequencies on Olimex-LPC-P2138 board
*
* Copyright HighTec EDV-Systeme GmbH 1982-2007
*====================================================================*/


#include "lpc213x.h"

#define FREQ_OSC		14745600	/* External oscillator frequency */
#define FREQ_PLL		235929600	/* PLL output frequency (M=4,P=2) */
#define FREQ_MCK		58982400	/* CPU frequency */


#endif /* __OLIMEX_LPC_P2138_H__ */
