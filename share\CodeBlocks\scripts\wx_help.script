/*
 * This file is part of the Code::Blocks IDE and licensed under the GNU General Public License, version 3
 * http://www.gnu.org/licenses/gpl-3.0.html
 *
 * $Revision: 12061 $
 * $Id: wx_help.script 12061 2020-04-17 17:18:58Z fuscated $
 * $HeadURL: http://svn.code.sf.net/p/codeblocks/code/trunk/src/scripts/wx_help.script $
 */

/*
  Sample help script for wxWidgets docs.
  Based on the original unix shell script by rjmyst3
*/
function SearchHelp(keyword)
{
    // that's all you should ever need to change in here
    local wx_version = _T("2.8")
    // on windows, adjust this for your wx installation
    local wx_doc_folder = _T("/usr/share/doc")

    if (PLATFORM == PLATFORM_GTK)
        wx_doc_folder += _T("/wx") + wx_version + _T("-doc")


    //
    // normally, you shouldn't have to edit anything below this point
    //

    local helproot = wx_doc_folder + _T("/wx-manual.html/")
    if (!IO.DirectoryExists(helproot))
    {
        local msg = _("wxWidgets documentation not found. Its expected location is:\n\n");
        msg += wx_doc_folder;
        msg += _T("\n\n");
        msg += _("If it is not installed, please install it and try again.\n");
        msg += _("If it is installed to a different location, you can edit the wx_help.script to reflect that.");
        ShowWarning(msg);
        return;
    }

    local prefix = _T("wx") + wx_version + _T("-manual_")

    // replace "contents" with "classref" below to default to alphabetical class list
    local defaultpath = prefix + _T("contents.html")

    // If there is no keyword, launch the default page defined above
    if (keyword.IsEmpty())
    {
        App.Open(helproot + defaultpath, false)
        return
    }

    // convert keyword to lowercase, this should be the class name
    keyword.MakeLower()
    local classpath = helproot + prefix + keyword + _T(".html")

    LogDebug(_T("Opening ") + classpath)
    if (IO.FileExists(classpath))
    {
        App.Open(classpath, false)
    }
    else
    {
        LogDebug(_("Not found, opening default page"))
        App.Open(helproot + defaultpath, false)
    }
}
