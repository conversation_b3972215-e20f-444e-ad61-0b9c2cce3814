<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_template_file>
<CodeBlocks_template_file>
	<Template name="DirectX" title="DirectX Project" category="2D/3D Graphics" bitmap="directx.png">
		<Notice value="This template expects the global variable &quot;dx&quot; to point
		               to the appropriate DirectX SDK.
                   This is the MS DirectX SDK for the Visual C++ Toolkit 2003 project
                   and e.g. the DevPack folder for the GCC project.

                   In addition for the Visual C++ Toolkit 2003 the global variable
                   &quot;psdk&quot; must point to the directory of the MS platform SDK.

                   You will be asked to setup the variables accordingly. If this is
                   not the case, verify &quot;Settings->Global variables&quot;"
				isWarning="1"/>
		<FileSet name="s" title="Default">
			<File source="directx_main.cpp" destination="main.cpp"/>
		</FileSet>
		<Option name="Free VisualC++ Toolkit 2003 project">
			<Project file="directx_vctk.cbp" useDefaultCompiler="0"/>
		</Option>
		<Option name="GCC project">
			<Project file="directx_gcc.cbp" useDefaultCompiler="0"/>
		</Option>
	</Template>
</CodeBlocks_template_file>
