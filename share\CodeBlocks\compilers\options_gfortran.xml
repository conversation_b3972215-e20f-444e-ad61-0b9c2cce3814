﻿<?xml version="1.0"?>
<!DOCTYPE CodeBlocks_compiler_options>
<CodeBlocks_compiler_options>
    <if platform="windows">
        <Program name="C"         value="mingw32-gfortran.exe"/>
        <Program name="CPP"       value="mingw32-gfortran.exe"/>
        <Program name="LD"        value="mingw32-gfortran.exe"/>
        <Program name="DBGconfig" value="gdb_debugger:Default"/>
        <Program name="LIB"       value="ar.exe"/>
        <Program name="WINDRES"   value="windres.exe"/>
        <Program name="MAKE"      value="mingw32-make.exe"/>
    </if>
    <else>
        <Program name="C"         value="gfortran"/>
        <Program name="CPP"       value="gfortran"/>
        <Program name="LD"        value="gfortran"/>
        <Program name="DBGconfig" value="gdb_debugger:Default"/>
        <Program name="LIB"       value="ar"/>
        <Program name="WINDRES"   value=""/>
        <Program name="MAKE"      value="make"/>
    </else>

    <Switch name="includeDirs"             value="-I"/>
    <Switch name="libDirs"                 value="-L"/>
    <Switch name="linkLibs"                value="-l"/>
    <Switch name="defines"                 value="-D"/>
    <Switch name="genericSwitch"           value="-"/>
    <Switch name="objectExtension"         value="o"/>
    <Switch name="needDependencies"        value="true"/>
    <Switch name="forceCompilerUseQuotes"  value="false"/>
    <Switch name="forceLinkerUseQuotes"    value="false"/>
    <Switch name="logging"                 value="default"/>
    <Switch name="libPrefix"               value="lib"/>
    <Switch name="libExtension"            value="a"/>
    <Switch name="linkerNeedsLibPrefix"    value="false"/>
    <Switch name="linkerNeedsLibExtension" value="false"/>
    <Switch name="UseFullSourcePaths"      value="true"/>

    <Option name="Produce debugging symbols"
            option="-g"
            category="Debugging"
            checkAgainst="-O -O1 -O2 -O3 -Os"
            checkMessage="You have optimizations enabled. This is Not A Good Thing(tm) when producing debugging symbols..."
            supersedes="-s"/>

    <Category name="Run-time checks">
        <Option name="Enable all run-time test"
                option="-fcheck=all"/>
        <Option name="Run-time check for array subscripts"
                option="-fcheck=bounds"/>
    </Category>

    <if platform="windows">
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg -lgmon"
                supersedes="-s"/>
    </if>
    <else>
        <Option name="Profile code when executed"
                option="-pg"
                category="Profiling"
                additionalLibs="-pg"
                supersedes="-s"/>
    </else>

    <Category name="Warnings">
        <Option name="Enable all compiler warnings (overrides many other settings)"
                option="-Wall"
                supersedes="-w"/>
        <Option name="Enable some extra warning flags"
                option="-Wextra"/>
        <Option name="Enable standard compiler warnings"
                option="-W"/>
        <Option name="Stop compiling after first error"
                option="-Wfatal-errors"/>
        <Option name="Inhibit all warning messages"
                option="-w"
                supersedes="-Wall -Wextra -W -pedantic -Warray-temporaries -Wconversion -Wimplicit-interface"/>
        <Option name="Turn all warnings into errors"
                option="-Werror"/>
        <Option name="Issue warnings for uses of extensions to Fortran 95"
                option="-pedantic"/>
        <Option name="Warn about array temporaries generated by the compiler"
                option="-Warray-temporaries"/>
        <Option name="Warn about implicit conversions between different types"
                option="-Wconversion"/>
        <Option name="Warn if a procedure is called without an explicit interface"
                option="-Wimplicit-interface"/>
        <Option name="Warn when code is inserted for (re)allocation of an allocatable array in assignments."
                option="-Wrealloc-lhs"/>
        <Option name="Warn when code is inserted for (re)allocation of scalars and derived types in assignments."
                option="-Wrealloc-lhs-all"/>
    </Category>

    <Category name="Fortran dialect">
        <Option name="The free form layout used by the source files"
                option="-ffree-form"
                supersedes="-ffree-form"/>
        <Option name="The fixed form layout used by the source files"
                option="-ffixed-form"
                supersedes="-ffixed-form"/>
        <Option name="Set the default real type to an 8 byte wide type"
                option="-fdefault-real-8"/>
        <Option name="Set the DOUBLE PRECISION type to an 8 byte wide type"
                option="-fdefault-double-8"/>
        <Option name="Set the default integer and logical types to an 8 byte wide type"
                option="-fdefault-integer-8"/>
        <Option name="Backslash as'C-style' escape characters"
                option="-fbackslash"/>
        <Option name="Set the default accessibility of module entities to PRIVATE"
                option="-fmodule-private"/>
        <Option name="In fixed form an entire line is meaningful"
                option="-ffixed-line-length-none"/>
        <Option name="In free form an entire line is meaningful"
                option="-ffree-line-length-none"/>
        <Option name="No implicit typing is allowed"
                option="-fimplicit-none"/>
        <Option name="Enable the OpenMP extensions"
                option="-fopenmp"
                additionalLibs="-fopenmp"/>
        <Option name="Strict conformance to the Fortran 95 standard"
                option="-std=f95"
                supersedes="-std=f2003 -std=f2008"/>
        <Option name="Strict conformance to the Fortran 2003 standard"
                option="-std=f2003"
                supersedes="-std=f95 -std=f2008"/>
        <Option name="Strict conformance to the Fortran 2008 standard"
                option="-std=f2008"
                supersedes="-std=f95 -std=f2003"/>
        <Option name="Disable automatic (re)allocation of left-hand side allocatable variables in assignments"
                option="-fno-realloc-lhs"/>
        <Option name="Enable preprocessing"
                option="-cpp"
                supersedes="-nocpp"/>
        <Option name="Disable preprocessing"
                option="-nocpp"
                supersedes="-cpp"/>
    </Category>

    <Common name="optimization"/>
    <Option name="Allow fast-math computation"
            option="-ffast-math"
            category="Optimization"
            checkAgainst="-g -ggdb"
            checkMessage="You have debugging symbols enabled. This is Not A Good Thing(tm) when optimizing..."/>

    <Common name="architecture"/>

    <Command name="CompileObject"
             value="$compiler -J$objects_output_dir $options $includes -c $file -o $object"/>
    <Command name="GenDependencies"
             value="$compiler -MM $options -MF $dep_object -MT $object $includes $file"/>
    <Command name="CompileResource"
             value="$rescomp $res_includes $res_options -J rc -O coff -i $file -o $resource_output"/>
    <Command name="LinkConsoleExe"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
    <if platform="windows">
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs -mwindows"/>
        <Command name="LinkDynamic"
                 value="$linker -shared -Wl,--output-def=$def_output -Wl,--out-implib=$static_output -Wl,--dll $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </if>
    <else>
        <Command name="LinkExe"
                 value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>
        <Command name="LinkDynamic"
                 value="$linker -shared $libdirs $link_objects $link_resobjects -o $exe_output $link_options $libs"/>
    </else>
    <Command name="LinkStatic"
             value="$lib_linker -r $static_output $link_objects\nranlib $static_output"/>
    <Command name="LinkNative"
             value="$linker $libdirs -o $exe_output $link_objects $link_resobjects $link_options $libs"/>

    <RegEx name="Fatal error"
           type="error"
           msg="4;5"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+)[\.:]{1}([0-9]+):.*(Fatal Error:.*)at \([0-9]+\)(.*)]]>
    </RegEx>
    <RegEx name="Fatal error"
           type="error"
           msg="4;5"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):([0-9]+):.*([Ff]+atal [Ee]+rror:.*: )(.*)]]>
    </RegEx>
    <RegEx name="Fatal error"
           type="error"
           msg="1">
        <![CDATA[[ \t]*(Fatal Error:.*)]]>
    </RegEx>
    <RegEx name="Warning"
           type="warning"
           msg="1;4"
           file="2"
           line="3">
        <![CDATA[([Ww]arning:)[ \t]+([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):([ \t]+Illegal.*)]]>
    </RegEx>
    <RegEx name="Info line"
           type="info"
           msg="0"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+)([\.:]{1}[0-9]+(\-[0-9]+)?:)[\t ]*$]]>
    </RegEx>
    <RegEx name="Compiler error"
           type="error"
           msg="1">
        <![CDATA[.*(Error: Inconsistent.*)]]>
    </RegEx>
    <RegEx name="Compiler error(4)"
           type="error"
           msg="1;4">
        <![CDATA[.*(Error:.*)(at )+\([0-9]+\)( *)(.*)]]>
    </RegEx>
    <RegEx name="Compiler warning"
           type="warning"
           msg="1;4">
        <![CDATA[.*(Warning:.*)(at )+\([0-9]+\)( *)(.*)]]>
    </RegEx>
    <RegEx name="Undefined reference"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+): (undefined reference.*)]]>
    </RegEx>
    <RegEx name="Compiler warning (2)"
           type="warning"
           msg="4;3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ ]*([0-9]+)[ ]*(:[ ]*[0-9]+)[ ]*:[ \t]*(warning:.*)]]>
    </RegEx>
    <RegEx name="Compiler warning (3)"
           type="warning"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):[ ]*([0-9]+)[ ]*:[ \t]*(warning:.*)]]>
    </RegEx>
    <RegEx name="Note"
           type="info"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]([Nn]ote:.*)]]>
    </RegEx>
    <RegEx name="Error"
           type="error"
           msg="4"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):([0-9]+):[ \t]+([Ee]rror.*)]]>
    </RegEx>
    <RegEx name="Error"
           type="error"
           msg="3"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):[ \t]+([Ee]rror.*)]]>
    </RegEx>
    <RegEx name="Include in..."
           type="normal"
           msg="0"
           file="0"
           line="0">
        <![CDATA[(Included at [][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):]]>
    </RegEx>
    <RegEx name="Info line (2)"
           type="info"
           msg="0"
           file="1"
           line="2">
        <![CDATA[([][{}() \t#%$~[:alnum:]&_:+/\.-]+):([0-9]+):]]>
    </RegEx>
    <RegEx name="Internal compiler error"
           type="error"
           msg="1;4">
        <![CDATA[.*(Internal Error.*)(at )+\([0-9]+\)( *)(.*)]]>
    </RegEx>
    <RegEx name="Unrecognized option"
           type="warning"
           msg="1">
        <![CDATA[[ \t]*(gfortran: unrecognized option.*)]]>
    </RegEx>
    <RegEx name="No file error"
           type="error"
           msg="1;2;3">
        <![CDATA[([ \t]*gfortran.*:)([][{}() \t#%$~[:alnum:]&_:+/\.-]+)(:[ \t]*No such file or directory.*)]]>
    </RegEx>
    <Common name="re-gf"/>
</CodeBlocks_compiler_options>
